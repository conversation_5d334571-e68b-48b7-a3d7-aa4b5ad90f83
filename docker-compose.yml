version: '3.8'

services:
  mongodb:
    image: mongo:7.0
    container_name: demo-mongodb
    restart: unless-stopped
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password
      MONGO_INITDB_DATABASE: demoDb
    volumes:
      - mongodb_data:/data/db
      - ./init-mongo.js:/docker-entrypoint-initdb.d/init-mongo.js:ro
    networks:
      - demo-network

  # 可选：MongoDB管理界面
  mongo-express:
    image: mongo-express:1.0.0
    container_name: demo-mongo-express
    restart: unless-stopped
    ports:
      - "8081:8081"
    environment:
      ME_CONFIG_MONGODB_ADMINUSERNAME: admin
      ME_CONFIG_MONGODB_ADMINPASSWORD: password
      ME_CONFIG_MONGODB_URL: **************************************/
      ME_CONFIG_BASICAUTH: false
    depends_on:
      - mongodb
    networks:
      - demo-network

volumes:
  mongodb_data:

networks:
  demo-network:
    driver: bridge
