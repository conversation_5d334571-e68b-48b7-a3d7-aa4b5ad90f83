package request

import (
	"fmt"
	"reflect"
	"strings"
	"time"

	"go.mongodb.org/mongo-driver/v2/bson/primitive"
)

// ==================== 通用请求接口 ====================

// Validator 验证器接口
type Validator interface {
	Validate() error
}

// ModelConverter 模型转换器接口
type ModelConverter[T any] interface {
	ToModel() *T
}

// UpdateMapConverter 更新映射转换器接口
type UpdateMapConverter interface {
	ToUpdateMap() map[string]interface{}
}

// ==================== 通用请求基类 ====================

// BaseRequest 基础请求结构
type BaseRequest struct {
	RequestID string    `json:"-" form:"-"` // 请求ID，用于追踪
	Timestamp time.Time `json:"-" form:"-"` // 请求时间戳
}

// SetRequestID 设置请求ID
func (br *BaseRequest) SetRequestID(id string) {
	br.RequestID = id
}

// SetTimestamp 设置时间戳
func (br *BaseRequest) SetTimestamp(t time.Time) {
	br.Timestamp = t
}

// ==================== 分页请求基类 ====================

// PaginationRequest 分页请求基类
type PaginationRequest struct {
	BaseRequest
	Page  int    `form:"page" validate:"omitempty,min=1"`
	Size  int    `form:"size" validate:"omitempty,min=1,max=100"`
	Order string `form:"order" validate:"omitempty"`
}

// GetPage 获取页码
func (pr *PaginationRequest) GetPage() int {
	if pr.Page <= 0 {
		return 1
	}
	return pr.Page
}

// GetSize 获取页大小
func (pr *PaginationRequest) GetSize() int {
	if pr.Size <= 0 {
		return 10
	}
	if pr.Size > 100 {
		return 100
	}
	return pr.Size
}

// GetSkip 获取跳过数量
func (pr *PaginationRequest) GetSkip() int64 {
	return int64((pr.GetPage() - 1) * pr.GetSize())
}

// GetLimit 获取限制数量
func (pr *PaginationRequest) GetLimit() int64 {
	return int64(pr.GetSize())
}

// ==================== ID请求基类 ====================

// IDRequest ID请求基类
type IDRequest struct {
	BaseRequest
	ID string `uri:"id" validate:"required,objectid"`
}

// GetObjectID 获取ObjectID
func (ir *IDRequest) GetObjectID() (primitive.ObjectID, error) {
	return primitive.ObjectIDFromHex(ir.ID)
}

// ==================== 批量操作请求基类 ====================

// BatchRequest 批量操作请求基类
type BatchRequest struct {
	BaseRequest
	IDs []string `json:"ids" validate:"required,min=1,max=100"`
}

// GetObjectIDs 获取ObjectID列表
func (br *BatchRequest) GetObjectIDs() ([]primitive.ObjectID, error) {
	objectIDs := make([]primitive.ObjectID, len(br.IDs))
	for i, id := range br.IDs {
		objectID, err := primitive.ObjectIDFromHex(id)
		if err != nil {
			return nil, fmt.Errorf("无效的ID格式: %s", id)
		}
		objectIDs[i] = objectID
	}
	return objectIDs, nil
}

// ==================== 时间范围请求基类 ====================

// TimeRangeRequest 时间范围请求基类
type TimeRangeRequest struct {
	BaseRequest
	StartTime time.Time `form:"start_time" validate:"omitempty"`
	EndTime   time.Time `form:"end_time" validate:"omitempty"`
}

// Validate 验证时间范围
func (tr *TimeRangeRequest) Validate() error {
	if !tr.StartTime.IsZero() && !tr.EndTime.IsZero() {
		if tr.StartTime.After(tr.EndTime) {
			return fmt.Errorf("开始时间不能晚于结束时间")
		}
	}
	return nil
}

// ==================== 通用CRUD请求组合 ====================

// CRUDRequests CRUD请求组合
type CRUDRequests[T any] struct {
	Create CreateRequest[T]
	Update UpdateRequest[T]
	Query  QueryRequest
	GetByID IDRequest
	Delete  IDRequest
}

// CreateRequest 通用创建请求
type CreateRequest[T any] struct {
	BaseRequest
	Data T `json:"data" validate:"required"`
}

// ToModel 转换为模型
func (cr *CreateRequest[T]) ToModel() *T {
	return &cr.Data
}

// UpdateRequest 通用更新请求
type UpdateRequest[T any] struct {
	IDRequest
	Data T `json:"data" validate:"required"`
}

// ToUpdateMap 转换为更新映射
func (ur *UpdateRequest[T]) ToUpdateMap() map[string]interface{} {
	updateMap := make(map[string]interface{})
	
	// 使用反射将结构体转换为map
	v := reflect.ValueOf(ur.Data)
	t := reflect.TypeOf(ur.Data)
	
	for i := 0; i < v.NumField(); i++ {
		field := v.Field(i)
		fieldType := t.Field(i)
		
		// 跳过空值
		if field.IsZero() {
			continue
		}
		
		// 获取json标签作为字段名
		jsonTag := fieldType.Tag.Get("json")
		if jsonTag == "" || jsonTag == "-" {
			continue
		}
		
		// 处理omitempty
		fieldName := strings.Split(jsonTag, ",")[0]
		updateMap[fieldName] = field.Interface()
	}
	
	// 添加更新时间
	updateMap["updated_at"] = time.Now()
	
	return updateMap
}

// QueryRequest 通用查询请求
type QueryRequest struct {
	PaginationRequest
	Filters map[string]interface{} `form:"-" json:"-"` // 动态过滤条件
}

// AddFilter 添加过滤条件
func (qr *QueryRequest) AddFilter(key string, value interface{}) {
	if qr.Filters == nil {
		qr.Filters = make(map[string]interface{})
	}
	qr.Filters[key] = value
}

// GetFilters 获取过滤条件
func (qr *QueryRequest) GetFilters() map[string]interface{} {
	if qr.Filters == nil {
		return make(map[string]interface{})
	}
	return qr.Filters
}

// ==================== 响应状态请求 ====================

// StatusUpdateRequest 状态更新请求
type StatusUpdateRequest struct {
	IDRequest
	Status string `json:"status" validate:"required"`
}

// ==================== 搜索请求基类 ====================

// SearchRequest 搜索请求基类
type SearchRequest struct {
	PaginationRequest
	Keyword   string   `form:"keyword" validate:"omitempty,min=1,max=100"`
	Fields    []string `form:"fields" validate:"omitempty"`
	Highlight bool     `form:"highlight" validate:"omitempty"`
}

// GetSearchFields 获取搜索字段
func (sr *SearchRequest) GetSearchFields() []string {
	if len(sr.Fields) == 0 {
		return []string{"name", "content", "description"} // 默认搜索字段
	}
	return sr.Fields
}

// ==================== 导出请求基类 ====================

// ExportRequest 导出请求基类
type ExportRequest struct {
	BaseRequest
	Format  string                 `form:"format" validate:"omitempty,oneof=csv xlsx json"`
	Fields  []string               `form:"fields" validate:"omitempty"`
	Filters map[string]interface{} `form:"-" json:"-"`
}

// GetFormat 获取导出格式
func (er *ExportRequest) GetFormat() string {
	if er.Format == "" {
		return "csv"
	}
	return er.Format
}

// GetExportFields 获取导出字段
func (er *ExportRequest) GetExportFields() []string {
	if len(er.Fields) == 0 {
		return []string{"id", "name", "created_at", "updated_at"} // 默认导出字段
	}
	return er.Fields
}
