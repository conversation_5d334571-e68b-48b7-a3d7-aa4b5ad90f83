package request

import (
	"demo/model"
	"fmt"
	"strings"
	"time"
)

// ==================== 任务相关请求结构体 ====================

// TaskCreateRequest 任务创建请求
type TaskCreateRequest struct {
	Name    string `json:"name" validate:"required,min=1,max=100"`
	Content string `json:"content" validate:"max=1000"`
}

// ToModel 转换为任务模型
func (req *TaskCreateRequest) ToModel() *model.Task {
	task := &model.Task{
		Name:    strings.TrimSpace(req.Name),
		Content: strings.TrimSpace(req.Content),
	}
	task.DefaultCreatedAt()
	task.DefaultUpdatedAt()
	return task
}

// Validate 自定义验证
func (req *TaskCreateRequest) Validate() error {
	if strings.TrimSpace(req.Name) == "" {
		return fmt.Errorf("任务名称不能为空")
	}
	if len(req.Content) > 1000 {
		return fmt.Errorf("任务内容不能超过1000个字符")
	}
	return nil
}

// TaskUpdateRequest 任务更新请求
type TaskUpdateRequest struct {
	Name    string `json:"name,omitempty" validate:"omitempty,min=1,max=100"`
	Content string `json:"content,omitempty" validate:"omitempty,max=1000"`
}

// ToUpdateMap 转换为更新映射
func (req *TaskUpdateRequest) ToUpdateMap() map[string]interface{} {
	updateMap := make(map[string]interface{})
	
	if req.Name != "" {
		updateMap["name"] = strings.TrimSpace(req.Name)
	}
	if req.Content != "" {
		updateMap["content"] = strings.TrimSpace(req.Content)
	}
	
	// 总是更新时间戳
	updateMap["updated_at"] = time.Now()
	
	return updateMap
}

// TaskQueryRequest 任务查询请求
type TaskQueryRequest struct {
	Name__like    string `form:"name" validate:"omitempty,min=1,max=100"`
	Content__like string `form:"content" validate:"omitempty,max=1000"`
	Page          int    `form:"page" validate:"omitempty,min=1"`
	Size          int    `form:"size" validate:"omitempty,min=1,max=100"`
	Order         string `form:"order" validate:"omitempty"`
}

// GetPage 获取页码
func (req *TaskQueryRequest) GetPage() int {
	if req.Page <= 0 {
		return 1
	}
	return req.Page
}

// GetSize 获取页大小
func (req *TaskQueryRequest) GetSize() int {
	if req.Size <= 0 {
		return 10
	}
	if req.Size > 100 {
		return 100
	}
	return req.Size
}

// TaskGetByIDRequest 根据ID获取任务请求
type TaskGetByIDRequest struct {
	ID string `uri:"id" validate:"required,objectid"`
}

// TaskDeleteRequest 删除任务请求
type TaskDeleteRequest struct {
	ID string `uri:"id" validate:"required,objectid"`
}

// ==================== 任务状态管理请求 ====================

// TaskStatusUpdateRequest 任务状态更新请求
type TaskStatusUpdateRequest struct {
	ID     string `uri:"id" validate:"required,objectid"`
	Status string `json:"status" validate:"required,oneof=pending in_progress completed cancelled"`
}

// TaskPriorityUpdateRequest 任务优先级更新请求
type TaskPriorityUpdateRequest struct {
	ID       string `uri:"id" validate:"required,objectid"`
	Priority int    `json:"priority" validate:"required,min=1,max=5"`
}

// ==================== 批量操作请求 ====================

// TaskBatchCreateRequest 批量创建任务请求
type TaskBatchCreateRequest struct {
	Tasks []TaskCreateRequest `json:"tasks" validate:"required,min=1,max=50"`
}

// TaskBatchDeleteRequest 批量删除任务请求
type TaskBatchDeleteRequest struct {
	IDs []string `json:"ids" validate:"required,min=1,max=100"`
}

// TaskBatchUpdateStatusRequest 批量更新任务状态请求
type TaskBatchUpdateStatusRequest struct {
	IDs    []string `json:"ids" validate:"required,min=1,max=100"`
	Status string   `json:"status" validate:"required,oneof=pending in_progress completed cancelled"`
}

// ==================== 高级查询请求 ====================

// TaskAdvancedQueryRequest 任务高级查询请求
type TaskAdvancedQueryRequest struct {
	// 基础查询
	TaskQueryRequest
	
	// 高级过滤
	Status__eq     string    `form:"status" validate:"omitempty,oneof=pending in_progress completed cancelled"`
	Priority__gte  int       `form:"priority_gte" validate:"omitempty,min=1,max=5"`
	Priority__lte  int       `form:"priority_lte" validate:"omitempty,min=1,max=5"`
	CreatedAt__gte time.Time `form:"created_at_gte" validate:"omitempty"`
	CreatedAt__lte time.Time `form:"created_at_lte" validate:"omitempty"`
	
	// 关联查询
	AssignedTo__eq string `form:"assigned_to" validate:"omitempty,objectid"`
	ProjectID__eq  string `form:"project_id" validate:"omitempty,objectid"`
	
	// 排序和分组
	GroupBy string `form:"group_by" validate:"omitempty,oneof=status priority assigned_to project_id"`
	Having  string `form:"having" validate:"omitempty"`
}

// ==================== 统计查询请求 ====================

// TaskStatsRequest 任务统计请求
type TaskStatsRequest struct {
	DateFrom   time.Time `form:"date_from" validate:"omitempty"`
	DateTo     time.Time `form:"date_to" validate:"omitempty"`
	GroupBy    string    `form:"group_by" validate:"omitempty,oneof=day week month status priority"`
	ProjectID  string    `form:"project_id" validate:"omitempty,objectid"`
	AssignedTo string    `form:"assigned_to" validate:"omitempty,objectid"`
}
