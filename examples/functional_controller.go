package examples

import (
	"context"
	"demo/database"
	"demo/model"
	"demo/response"
	"demo/service"
	"demo/utils"

	"github.com/gin-gonic/gin"
)

// ==================== 函数式处理器定义 ====================

// HandlerFunc 处理器函数类型
type HandlerFunc[T any] func(c *gin.Context) (*T, error)

// ValidatorFunc 验证器函数类型
type ValidatorFunc[T any] func(req *T) error

// TransformerFunc 转换器函数类型
type TransformerFunc[From, To any] func(from *From) *To

// BusinessFunc 业务逻辑函数类型
type BusinessFunc[T any] func(ctx context.Context, data *T) (*T, error)

// ==================== 函数式控制器构建器 ====================

// ControllerBuilder 控制器构建器
type ControllerBuilder[T any] struct {
	collection string
	repository *database.Repository[T]
	validators []ValidatorFunc[T]
	business   []BusinessFunc[T]
}

// NewControllerBuilder 创建控制器构建器
func NewControllerBuilder[T any](collection string) *ControllerBuilder[T] {
	return &ControllerBuilder[T]{
		collection: collection,
		repository: database.NewRepository[T](collection),
		validators: make([]ValidatorFunc[T], 0),
		business:   make([]BusinessFunc[T], 0),
	}
}

// WithValidator 添加验证器
func (cb *ControllerBuilder[T]) WithValidator(validator ValidatorFunc[T]) *ControllerBuilder[T] {
	cb.validators = append(cb.validators, validator)
	return cb
}

// WithBusiness 添加业务逻辑
func (cb *ControllerBuilder[T]) WithBusiness(business BusinessFunc[T]) *ControllerBuilder[T] {
	cb.business = append(cb.business, business)
	return cb
}

// BuildCreateHandler 构建创建处理器
func (cb *ControllerBuilder[T]) BuildCreateHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		var req T

		// 1. 绑定和基础验证
		if err := utils.BindAndValidate(c, &req, "json"); err != nil {
			return
		}

		// 2. 自定义验证
		for _, validator := range cb.validators {
			if err := validator(&req); err != nil {
				resp := response.BadRequest(err.Error())
				c.JSON(resp.GetHTTPStatus(), resp)
				return
			}
		}

		// 3. 业务逻辑处理
		data := &req
		for _, bizFunc := range cb.business {
			result, err := bizFunc(context.Background(), data)
			if err != nil {
				resp := response.BadRequest(err.Error())
				c.JSON(resp.GetHTTPStatus(), resp)
				return
			}
			data = result
		}

		// 4. 数据持久化
		result, err := cb.repository.Create(context.Background(), data)
		if err != nil {
			resp := response.InternalError("创建失败: " + err.Error())
			c.JSON(resp.GetHTTPStatus(), resp)
			return
		}

		resp := response.SuccessWithMessage(result, "创建成功")
		c.JSON(resp.GetHTTPStatus(), resp)
	}
}

// BuildQueryHandler 构建查询处理器
func (cb *ControllerBuilder[T]) BuildQueryHandler() gin.HandlerFunc {
	paginationService := service.NewPaginationService[T](cb.collection)
	
	return func(c *gin.Context) {
		// 动态绑定查询参数（这里简化处理）
		queryParams := make(map[string]interface{})
		
		// 从查询参数中提取
		for key, values := range c.Request.URL.Query() {
			if len(values) > 0 {
				queryParams[key] = values[0]
			}
		}

		pageResp, err := paginationService.QueryWithPagination(context.Background(), queryParams)
		if err != nil {
			resp := response.InternalError("查询失败: " + err.Error())
			c.JSON(resp.GetHTTPStatus(), resp)
			return
		}

		resp := response.SuccessWithMessage(pageResp, "查询成功")
		c.JSON(resp.GetHTTPStatus(), resp)
	}
}

// BuildGetByIDHandler 构建获取详情处理器
func (cb *ControllerBuilder[T]) BuildGetByIDHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		id := c.Param("id")
		if id == "" {
			resp := response.BadRequest("ID不能为空")
			c.JSON(resp.GetHTTPStatus(), resp)
			return
		}

		result, err := utils.FindByID[T](cb.collection, id)
		if err != nil {
			resp := response.NotFound("记录不存在")
			c.JSON(resp.GetHTTPStatus(), resp)
			return
		}

		resp := response.Success(result)
		c.JSON(resp.GetHTTPStatus(), resp)
	}
}

// BuildUpdateHandler 构建更新处理器
func (cb *ControllerBuilder[T]) BuildUpdateHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		id := c.Param("id")
		if id == "" {
			resp := response.BadRequest("ID不能为空")
			c.JSON(resp.GetHTTPStatus(), resp)
			return
		}

		var req T
		if err := utils.BindAndValidate(c, &req, "json"); err != nil {
			return
		}

		// 自定义验证
		for _, validator := range cb.validators {
			if err := validator(&req); err != nil {
				resp := response.BadRequest(err.Error())
				c.JSON(resp.GetHTTPStatus(), resp)
				return
			}
		}

		err := utils.UpdateByID[T](cb.collection, id, req)
		if err != nil {
			resp := response.InternalError("更新失败: " + err.Error())
			c.JSON(resp.GetHTTPStatus(), resp)
			return
		}

		resp := response.SuccessWithMessage(nil, "更新成功")
		c.JSON(resp.GetHTTPStatus(), resp)
	}
}

// BuildDeleteHandler 构建删除处理器
func (cb *ControllerBuilder[T]) BuildDeleteHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		id := c.Param("id")
		if id == "" {
			resp := response.BadRequest("ID不能为空")
			c.JSON(resp.GetHTTPStatus(), resp)
			return
		}

		err := utils.DeleteByID[T](cb.collection, id)
		if err != nil {
			resp := response.InternalError("删除失败: " + err.Error())
			c.JSON(resp.GetHTTPStatus(), resp)
			return
		}

		resp := response.SuccessWithMessage(nil, "删除成功")
		c.JSON(resp.GetHTTPStatus(), resp)
	}
}

// ==================== Task 函数式实现 ====================

// TaskRequest 统一的任务请求结构
type TaskRequest struct {
	Name    string `json:"name" form:"name" validate:"required,min=1,max=100"`
	Content string `json:"content" form:"content" validate:"max=1000"`
	// 查询字段
	Name__like    string `form:"name" validate:"omitempty,min=1,max=100"`
	Content__like string `form:"content" validate:"omitempty,max=1000"`
	Page          int    `form:"page" validate:"omitempty,min=1"`
	Size          int    `form:"size" validate:"omitempty,min=1,max=100"`
	Order         string `form:"order" validate:"omitempty"`
}

// ToTask 转换为Task模型
func (req *TaskRequest) ToTask() *model.Task {
	task := &model.Task{
		Name:    req.Name,
		Content: req.Content,
	}
	task.DefaultCreatedAt()
	task.DefaultUpdatedAt()
	return task
}

// ==================== 函数式验证器和业务逻辑 ====================

// TaskNameValidator 任务名称验证器
func TaskNameValidator(req *TaskRequest) error {
	if len(strings.TrimSpace(req.Name)) == 0 {
		return fmt.Errorf("任务名称不能为空")
	}
	return nil
}

// TaskContentValidator 任务内容验证器
func TaskContentValidator(req *TaskRequest) error {
	if len(req.Content) > 1000 {
		return fmt.Errorf("任务内容不能超过1000个字符")
	}
	return nil
}

// TaskBusinessLogic 任务业务逻辑
func TaskBusinessLogic(ctx context.Context, req *TaskRequest) (*TaskRequest, error) {
	// 这里可以添加复杂的业务逻辑
	// 例如：检查重复、数据清洗、关联数据处理等
	
	// 示例：清理数据
	req.Name = strings.TrimSpace(req.Name)
	req.Content = strings.TrimSpace(req.Content)
	
	return req, nil
}

// ==================== 路由注册 ====================

func init() {
	// 创建Task控制器构建器
	taskBuilder := NewControllerBuilder[TaskRequest]("task").
		WithValidator(TaskNameValidator).
		WithValidator(TaskContentValidator).
		WithBusiness(TaskBusinessLogic)

	// 注册路由
	router.Api.POST("/task/functional", taskBuilder.BuildCreateHandler())
	router.Api.GET("/task/functional/query", taskBuilder.BuildQueryHandler())
	router.Api.GET("/task/functional/:id", taskBuilder.BuildGetByIDHandler())
	router.Api.PUT("/task/functional/:id", taskBuilder.BuildUpdateHandler())
	router.Api.DELETE("/task/functional/:id", taskBuilder.BuildDeleteHandler())
}
