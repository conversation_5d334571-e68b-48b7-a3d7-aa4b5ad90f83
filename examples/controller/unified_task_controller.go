package controller

import (
	"context"
	"demo/examples/request"
	"demo/model"
	"demo/response"
	"demo/router"
	"demo/service"
	"demo/utils"

	"github.com/gin-gonic/gin"
)

// ==================== 统一请求结构体的任务控制器 ====================

// UnifiedTaskController 统一请求结构体的任务控制器
type UnifiedTaskController struct {
	paginationService *service.PaginationService[model.Task]
}

// NewUnifiedTaskController 创建统一任务控制器
func NewUnifiedTaskController() *UnifiedTaskController {
	return &UnifiedTaskController{
		paginationService: service.NewPaginationService[model.Task]("task"),
	}
}

// ==================== CRUD操作 ====================

// CreateTask 创建任务
func (tc *UnifiedTaskController) CreateTask(c *gin.Context) {
	var req request.TaskCreateRequest

	// 1. 绑定和验证
	if err := utils.BindAndValidate(c, &req, "json"); err != nil {
		return
	}

	// 2. 自定义验证
	if err := req.Validate(); err != nil {
		resp := response.BadRequest(err.Error())
		c.JSON(resp.GetHTTPStatus(), resp)
		return
	}

	// 3. 转换为模型并创建
	task := req.ToModel()
	result, err := utils.CreateModel("task", task)
	if err != nil {
		resp := response.InternalError("创建任务失败: " + err.Error())
		c.JSON(resp.GetHTTPStatus(), resp)
		return
	}

	resp := response.SuccessWithMessage(result, "任务创建成功")
	c.JSON(resp.GetHTTPStatus(), resp)
}

// QueryTasks 查询任务列表
func (tc *UnifiedTaskController) QueryTasks(c *gin.Context) {
	var req request.TaskQueryRequest

	if err := utils.BindAndValidate(c, &req, "query"); err != nil {
		return
	}

	// 使用分页服务
	pageResp, err := tc.paginationService.QueryWithPagination(context.Background(), req)
	if err != nil {
		resp := response.InternalError("查询失败: " + err.Error())
		c.JSON(resp.GetHTTPStatus(), resp)
		return
	}

	resp := response.SuccessWithMessage(pageResp, "查询成功")
	c.JSON(resp.GetHTTPStatus(), resp)
}

// GetTaskByID 获取任务详情
func (tc *UnifiedTaskController) GetTaskByID(c *gin.Context) {
	var req request.TaskGetByIDRequest

	if err := utils.BindAndValidate(c, &req, "uri"); err != nil {
		return
	}

	task, err := utils.FindByID[model.Task]("task", req.ID)
	if err != nil {
		resp := response.NotFound("任务不存在")
		c.JSON(resp.GetHTTPStatus(), resp)
		return
	}

	resp := response.Success(task)
	c.JSON(resp.GetHTTPStatus(), resp)
}

// UpdateTask 更新任务
func (tc *UnifiedTaskController) UpdateTask(c *gin.Context) {
	var req request.TaskUpdateRequest

	// 绑定URI参数和JSON数据
	if err := utils.BindAndValidate(c, &req, "both"); err != nil {
		return
	}

	// 使用请求结构体的ToUpdateMap方法
	updateData := req.ToUpdateMap()
	if len(updateData) == 0 {
		resp := response.BadRequest("没有需要更新的数据")
		c.JSON(resp.GetHTTPStatus(), resp)
		return
	}

	err := utils.UpdateByIDWithMap[model.Task]("task", c.Param("id"), updateData)
	if err != nil {
		resp := response.InternalError("更新失败: " + err.Error())
		c.JSON(resp.GetHTTPStatus(), resp)
		return
	}

	resp := response.SuccessWithMessage(nil, "更新成功")
	c.JSON(resp.GetHTTPStatus(), resp)
}

// DeleteTask 删除任务
func (tc *UnifiedTaskController) DeleteTask(c *gin.Context) {
	var req request.TaskDeleteRequest

	if err := utils.BindAndValidate(c, &req, "uri"); err != nil {
		return
	}

	err := utils.DeleteByID[model.Task]("task", req.ID)
	if err != nil {
		resp := response.InternalError("删除失败: " + err.Error())
		c.JSON(resp.GetHTTPStatus(), resp)
		return
	}

	resp := response.SuccessWithMessage(nil, "删除成功")
	c.JSON(resp.GetHTTPStatus(), resp)
}

// ==================== 批量操作 ====================

// BatchCreateTasks 批量创建任务
func (tc *UnifiedTaskController) BatchCreateTasks(c *gin.Context) {
	var req request.TaskBatchCreateRequest

	if err := utils.BindAndValidate(c, &req, "json"); err != nil {
		return
	}

	// 转换为模型列表
	tasks := make([]*model.Task, len(req.Tasks))
	for i, taskReq := range req.Tasks {
		if err := taskReq.Validate(); err != nil {
			resp := response.BadRequest(fmt.Sprintf("第%d个任务验证失败: %s", i+1, err.Error()))
			c.JSON(resp.GetHTTPStatus(), resp)
			return
		}
		tasks[i] = taskReq.ToModel()
	}

	// 批量创建
	results, err := utils.BatchCreate("task", tasks)
	if err != nil {
		resp := response.InternalError("批量创建失败: " + err.Error())
		c.JSON(resp.GetHTTPStatus(), resp)
		return
	}

	resp := response.SuccessWithMessage(results, fmt.Sprintf("成功创建%d个任务", len(results)))
	c.JSON(resp.GetHTTPStatus(), resp)
}

// BatchDeleteTasks 批量删除任务
func (tc *UnifiedTaskController) BatchDeleteTasks(c *gin.Context) {
	var req request.TaskBatchDeleteRequest

	if err := utils.BindAndValidate(c, &req, "json"); err != nil {
		return
	}

	// 批量删除
	deletedCount, err := utils.BatchDelete[model.Task]("task", req.IDs)
	if err != nil {
		resp := response.InternalError("批量删除失败: " + err.Error())
		c.JSON(resp.GetHTTPStatus(), resp)
		return
	}

	resp := response.SuccessWithMessage(nil, fmt.Sprintf("成功删除%d个任务", deletedCount))
	c.JSON(resp.GetHTTPStatus(), resp)
}

// ==================== 高级功能 ====================

// UpdateTaskStatus 更新任务状态
func (tc *UnifiedTaskController) UpdateTaskStatus(c *gin.Context) {
	var req request.TaskStatusUpdateRequest

	if err := utils.BindAndValidate(c, &req, "both"); err != nil {
		return
	}

	updateData := map[string]interface{}{
		"status":     req.Status,
		"updated_at": time.Now(),
	}

	err := utils.UpdateByIDWithMap[model.Task]("task", req.ID, updateData)
	if err != nil {
		resp := response.InternalError("更新状态失败: " + err.Error())
		c.JSON(resp.GetHTTPStatus(), resp)
		return
	}

	resp := response.SuccessWithMessage(nil, "状态更新成功")
	c.JSON(resp.GetHTTPStatus(), resp)
}

// AdvancedQuery 高级查询
func (tc *UnifiedTaskController) AdvancedQuery(c *gin.Context) {
	var req request.TaskAdvancedQueryRequest

	if err := utils.BindAndValidate(c, &req, "query"); err != nil {
		return
	}

	// 使用高级查询服务
	pageResp, err := tc.paginationService.AdvancedQueryWithPagination(context.Background(), req)
	if err != nil {
		resp := response.InternalError("高级查询失败: " + err.Error())
		c.JSON(resp.GetHTTPStatus(), resp)
		return
	}

	resp := response.SuccessWithMessage(pageResp, "高级查询成功")
	c.JSON(resp.GetHTTPStatus(), resp)
}

// GetTaskStats 获取任务统计
func (tc *UnifiedTaskController) GetTaskStats(c *gin.Context) {
	var req request.TaskStatsRequest

	if err := utils.BindAndValidate(c, &req, "query"); err != nil {
		return
	}

	// 调用统计服务
	stats, err := tc.paginationService.GetStats(context.Background(), req)
	if err != nil {
		resp := response.InternalError("获取统计失败: " + err.Error())
		c.JSON(resp.GetHTTPStatus(), resp)
		return
	}

	resp := response.SuccessWithMessage(stats, "统计获取成功")
	c.JSON(resp.GetHTTPStatus(), resp)
}

// ==================== 路由注册 ====================

var unifiedTaskController = NewUnifiedTaskController()

func init() {
	// 基础CRUD
	router.Api.POST("/task/unified", unifiedTaskController.CreateTask)
	router.Api.GET("/task/unified/query", unifiedTaskController.QueryTasks)
	router.Api.GET("/task/unified/:id", unifiedTaskController.GetTaskByID)
	router.Api.PUT("/task/unified/:id", unifiedTaskController.UpdateTask)
	router.Api.DELETE("/task/unified/:id", unifiedTaskController.DeleteTask)

	// 批量操作
	router.Api.POST("/task/unified/batch", unifiedTaskController.BatchCreateTasks)
	router.Api.DELETE("/task/unified/batch", unifiedTaskController.BatchDeleteTasks)

	// 高级功能
	router.Api.PUT("/task/unified/:id/status", unifiedTaskController.UpdateTaskStatus)
	router.Api.GET("/task/unified/advanced/query", unifiedTaskController.AdvancedQuery)
	router.Api.GET("/task/unified/stats", unifiedTaskController.GetTaskStats)
}
