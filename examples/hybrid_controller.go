package examples

import (
	"context"
	"demo/database"
	"demo/model"
	"demo/response"
	"demo/service"
	"demo/utils"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/v2/bson"
)

// HybridUserController 混合架构示例
type HybridUserController struct {
	repository  *database.Repository[model.User]
	userService *service.UserService // 只在需要复杂业务逻辑时使用
}

func NewHybridUserController() *HybridUserController {
	return &HybridUserController{
		repository:  database.NewRepository[model.User]("user"),
		userService: service.NewUserService(),
	}
}

// ==================== 简单操作：直接使用两层架构 ====================

// GetUserRequest 获取用户请求
type GetUserRequest struct {
	ID string `uri:"id" validate:"required,objectid"`
}

// GetUser 获取单个用户 - 简单操作，直接调用数据层
func (uc *HybridUserController) GetUser(c *gin.Context) {
	var req GetUserRequest

	if err := utils.BindAndValidate(c, &req, "uri"); err != nil {
		return
	}

	// 直接调用数据层
	user, err := utils.FindByID[model.User]("user", req.ID)
	if err != nil {
		resp := response.NotFound("用户不存在")
		c.JSON(resp.GetHTTPStatus(), resp)
		return
	}

	// 清除敏感信息
	user.Password = ""
	resp := response.Success(user)
	c.JSON(resp.GetHTTPStatus(), resp)
}

// UpdateUserRequest 更新用户请求
type UpdateUserRequest struct {
	Username string `json:"username,omitempty" validate:"omitempty,min=3,max=50"`
	Email    string `json:"email,omitempty" validate:"omitempty,email"`
	Status   string `json:"status,omitempty" validate:"omitempty,oneof=active inactive"`
}

// UpdateUser 更新用户 - 简单操作，直接调用数据层
func (uc *HybridUserController) UpdateUser(c *gin.Context) {
	userID := c.Param("id")
	if userID == "" {
		resp := response.BadRequest("用户ID不能为空")
		c.JSON(resp.GetHTTPStatus(), resp)
		return
	}

	var req UpdateUserRequest
	if err := utils.BindAndValidate(c, &req, "json"); err != nil {
		return
	}

	// 直接调用工具函数更新
	err := utils.UpdateByID[model.User]("user", userID, req)
	if err != nil {
		resp := response.InternalError("更新失败: " + err.Error())
		c.JSON(resp.GetHTTPStatus(), resp)
		return
	}

	resp := response.SuccessWithMessage(nil, "更新成功")
	c.JSON(resp.GetHTTPStatus(), resp)
}

// DeleteUser 删除用户 - 简单操作，直接调用数据层
func (uc *HybridUserController) DeleteUser(c *gin.Context) {
	userID := c.Param("id")
	if userID == "" {
		resp := response.BadRequest("用户ID不能为空")
		c.JSON(resp.GetHTTPStatus(), resp)
		return
	}

	// 直接调用工具函数删除
	err := utils.DeleteByID[model.Task]("user", userID)
	if err != nil {
		resp := response.InternalError("删除失败: " + err.Error())
		c.JSON(resp.GetHTTPStatus(), resp)
		return
	}

	resp := response.SuccessWithMessage(nil, "删除成功")
	c.JSON(resp.GetHTTPStatus(), resp)
}

// ==================== 复杂操作：使用三层架构 ====================

// CreateUserRequest 创建用户请求 - 复杂业务逻辑
type CreateUserRequest struct {
	Username string `json:"username" validate:"required,min=3,max=50"`
	Email    string `json:"email" validate:"required,email"`
	Password string `json:"password" validate:"required,min=8"`
}

// CreateUser 创建用户 - 复杂业务逻辑，使用Service层
func (uc *HybridUserController) CreateUser(c *gin.Context) {
	var req CreateUserRequest

	if err := utils.BindAndValidate(c, &req, "json"); err != nil {
		return
	}

	// 转换为Service层请求（只在复杂业务时才需要）
	serviceReq := &service.CreateUserRequest{
		Username: req.Username,
		Email:    req.Email,
		Password: req.Password,
	}

	// 调用Service层处理复杂业务逻辑
	user, err := uc.userService.CreateUser(context.Background(), serviceReq)
	if err != nil {
		resp := response.BadRequest(err.Error())
		c.JSON(resp.GetHTTPStatus(), resp)
		return
	}

	user.Password = ""
	resp := response.SuccessWithMessage(user, "用户创建成功")
	c.JSON(resp.GetHTTPStatus(), resp)
}

// ChangePasswordRequest 修改密码请求 - 复杂业务逻辑
type ChangePasswordRequest struct {
	OldPassword string `json:"old_password" validate:"required"`
	NewPassword string `json:"new_password" validate:"required,min=8"`
}

// ChangePassword 修改密码 - 复杂业务逻辑，使用Service层
func (uc *HybridUserController) ChangePassword(c *gin.Context) {
	userID := c.Param("id")
	if userID == "" {
		resp := response.BadRequest("用户ID不能为空")
		c.JSON(resp.GetHTTPStatus(), resp)
		return
	}

	var req ChangePasswordRequest
	if err := utils.BindAndValidate(c, &req, "json"); err != nil {
		return
	}

	// 转换为Service层请求
	serviceReq := &service.ChangePasswordRequest{
		OldPassword: req.OldPassword,
		NewPassword: req.NewPassword,
	}

	// 调用Service层处理复杂业务逻辑（密码验证、加密等）
	err := uc.userService.ChangePassword(context.Background(), userID, serviceReq)
	if err != nil {
		resp := response.BadRequest(err.Error())
		c.JSON(resp.GetHTTPStatus(), resp)
		return
	}

	resp := response.SuccessWithMessage(nil, "密码修改成功")
	c.JSON(resp.GetHTTPStatus(), resp)
}

// ==================== 查询操作：使用两层架构 ====================

type UserQueryRequest struct {
	Username__like string `form:"username" validate:"omitempty,min=1,max=50"`
	Email__like    string `form:"email" validate:"omitempty,email"`
	Status__eq     string `form:"status" validate:"omitempty,oneof=active inactive"`
	Page           int    `form:"page" validate:"omitempty,min=1"`
	Size           int    `form:"size" validate:"omitempty,min=1,max=100"`
	Order          string `form:"order" validate:"omitempty"`
}

// QueryUsers 查询用户 - 简单查询，直接调用数据层
func (uc *HybridUserController) QueryUsers(c *gin.Context) {
	var req UserQueryRequest

	if err := utils.BindAndValidate(c, &req, "query"); err != nil {
		return
	}

	// 使用分页服务（这是一个通用的查询逻辑）
	paginationService := service.NewPaginationService[model.User]("user")
	
	pageResp, err := paginationService.QueryWithPagination(context.Background(), req)
	if err != nil {
		resp := response.InternalError("查询失败: " + err.Error())
		c.JSON(resp.GetHTTPStatus(), resp)
		return
	}

	// 清除敏感信息
	if users, ok := pageResp.Data.([]model.User); ok {
		for i := range users {
			users[i].Password = ""
		}
		pageResp.Data = users
	}

	resp := response.SuccessWithMessage(pageResp, "查询成功")
	c.JSON(resp.GetHTTPStatus(), resp)
}
