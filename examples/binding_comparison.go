package examples

import (
	"demo/response"
	"demo/validator"
	"net/http"

	"github.com/gin-gonic/gin"
)

// ==================== 原来的方式 ====================
type TaskQueryParamOld struct {
	Name string `form:"name"`
	Page int    `form:"page"`
	Size int    `form:"size"`
}

func GetTaskListOld(c *gin.Context) {
	var param TaskQueryParamOld
	err := c.BindQuery(&param)
	if err != nil {
		return // ❌ 问题：客户端收不到任何错误信息！
	}

	// ❌ 问题：没有参数验证
	// 如果 page 是负数或 size 超过限制，程序可能出错

	// ... 后续业务逻辑
}

// ==================== 新的方式 ====================
type TaskQueryParamNew struct {
	Name string `form:"name" validate:"omitempty,min=1,max=100"`
	Page int    `form:"page" validate:"omitempty,min=1"`
	Size int    `form:"size" validate:"omitempty,min=1,max=100"`
}

func GetTaskListNew(c *gin.Context) {
	var param TaskQueryParamNew
	
	// ✅ 优势：统一处理绑定和验证
	if err := bindAndValidate(c, &param, "query"); err != nil {
		return // 已经自动返回了错误响应
	}

	// ✅ 优势：参数已经验证过，可以安全使用
	// ... 后续业务逻辑
}

// ==================== bindAndValidate 实现 ====================
func bindAndValidate(c *gin.Context, obj interface{}, bindType string) error {
	var err error
	
	// 1. 参数绑定
	switch bindType {
	case "json":
		err = c.ShouldBindJSON(obj)
	case "query":
		err = c.ShouldBindQuery(obj)
	case "form":
		err = c.ShouldBind(obj)
	}
	
	if err != nil {
		// ✅ 返回具体的绑定错误信息
		resp := response.Fail("参数绑定失败: " + err.Error())
		c.JSON(http.StatusBadRequest, resp)
		return err
	}

	// 2. 参数验证
	if err := validator.Validate(obj); err != nil {
		// ✅ 返回详细的验证错误信息
		validationResp := validator.NewValidationErrorResponse(err)
		c.JSON(http.StatusBadRequest, validationResp)
		return err
	}

	return nil
}

// ==================== 实际效果对比 ====================

// 原来的方式 - 客户端请求错误参数时的响应：
// GET /task/query?page=-1&size=1000
// 响应：200 OK (但是没有任何内容，客户端不知道发生了什么)

// 新的方式 - 客户端请求错误参数时的响应：
// GET /task/query?page=-1&size=1000
// 响应：400 Bad Request
// {
//   "message": "Validation failed",
//   "errors": {
//     "page": "page must be at least 1",
//     "size": "size must be at most 100 characters"
//   }
// }

// ==================== 更多优势示例 ====================

// 1. 类型转换错误处理
// 原来：GET /task/query?page=abc
// 响应：200 OK (无内容) - 客户端不知道 page 参数格式错误

// 新的：GET /task/query?page=abc
// 响应：400 Bad Request
// {
//   "code": 400,
//   "message": "参数绑定失败: strconv.Atoi: parsing \"abc\": invalid syntax"
// }

// 2. 必填参数缺失
type UserCreateParam struct {
	Name  string `json:"name" validate:"required,min=2,max=50"`
	Email string `json:"email" validate:"required,email"`
}

func CreateUserOld(c *gin.Context) {
	var param UserCreateParam
	if err := c.ShouldBindJSON(&param); err != nil {
		return // ❌ 客户端不知道哪个字段有问题
	}
	// ❌ 没有验证，可能创建空名称的用户
}

func CreateUserNew(c *gin.Context) {
	var param UserCreateParam
	if err := bindAndValidate(c, &param, "json"); err != nil {
		return // ✅ 客户端收到详细的验证错误
	}
	// ✅ 参数已验证，可以安全使用
}

// 客户端发送空数据：POST /user/create {}
// 新方式响应：
// {
//   "message": "Validation failed",
//   "errors": {
//     "name": "name is required",
//     "email": "email is required"
//   }
// }

// ==================== 扩展性优势 ====================

// 可以轻松添加更多功能
func bindAndValidateAdvanced(c *gin.Context, obj interface{}, bindType string) error {
	// 1. 参数绑定
	if err := doBind(c, obj, bindType); err != nil {
		return err
	}

	// 2. 参数验证
	if err := validator.Validate(obj); err != nil {
		return err
	}

	// 3. ✅ 可以添加更多功能：
	// - 参数清理（去除前后空格）
	// - 参数转换（大小写转换）
	// - 安全检查（SQL注入检测）
	// - 日志记录
	// - 性能监控

	return nil
}

func doBind(c *gin.Context, obj interface{}, bindType string) error {
	var err error
	switch bindType {
	case "json":
		err = c.ShouldBindJSON(obj)
	case "query":
		err = c.ShouldBindQuery(obj)
	case "form":
		err = c.ShouldBind(obj)
	case "uri":
		err = c.ShouldBindUri(obj)
	case "header":
		err = c.ShouldBindHeader(obj)
	}
	
	if err != nil {
		resp := response.Fail("参数绑定失败: " + err.Error())
		c.JSON(http.StatusBadRequest, resp)
		return err
	}
	return nil
}
