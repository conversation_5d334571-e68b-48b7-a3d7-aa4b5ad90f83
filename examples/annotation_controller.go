package examples

import (
	"context"
	"demo/database"
	"demo/model"
	"demo/response"
	"demo/service"
	"demo/utils"
	"reflect"
	"strings"

	"github.com/gin-gonic/gin"
)

// ==================== 注解标签定义 ====================

// AutoController 自动控制器注解
type AutoController struct {
	Collection string                 // 集合名称
	Routes     map[string]RouteConfig // 路由配置
}

// RouteConfig 路由配置
type RouteConfig struct {
	Method   string // HTTP方法
	Path     string // 路径
	Handler  string // 处理器名称
	Business bool   // 是否需要业务逻辑层
}

// ==================== 自动化控制器基类 ====================

// BaseAutoController 自动化控制器基类
type BaseAutoController[T any] struct {
	collection        string
	repository        *database.Repository[T]
	paginationService *service.PaginationService[T]
	config            AutoController
}

// NewBaseAutoController 创建自动化控制器
func NewBaseAutoController[T any](config AutoController) *BaseAutoController[T] {
	return &BaseAutoController[T]{
		collection:        config.Collection,
		repository:        database.NewRepository[T](config.Collection),
		paginationService: service.NewPaginationService[T](config.Collection),
		config:            config,
	}
}

// AutoCreate 自动创建方法
func (ctrl *BaseAutoController[T]) AutoCreate(c *gin.Context, requestType reflect.Type) {
	// 动态创建请求结构体实例
	reqPtr := reflect.New(requestType)
	req := reqPtr.Interface()

	if err := utils.BindAndValidate(c, req, "json"); err != nil {
		return
	}

	// 检查是否有ToModel方法
	if method := reqPtr.MethodByName("ToModel"); method.IsValid() {
		results := method.Call(nil)
		if len(results) > 0 {
			model := results[0].Interface().(*T)
			
			result, err := ctrl.repository.Create(context.Background(), model)
			if err != nil {
				resp := response.InternalError("创建失败: " + err.Error())
				c.JSON(resp.GetHTTPStatus(), resp)
				return
			}

			resp := response.SuccessWithMessage(result, "创建成功")
			c.JSON(resp.GetHTTPStatus(), resp)
			return
		}
	}

	// 如果没有ToModel方法，直接转换
	if model, ok := req.(*T); ok {
		result, err := ctrl.repository.Create(context.Background(), model)
		if err != nil {
			resp := response.InternalError("创建失败: " + err.Error())
			c.JSON(resp.GetHTTPStatus(), resp)
			return
		}

		resp := response.SuccessWithMessage(result, "创建成功")
		c.JSON(resp.GetHTTPStatus(), resp)
	}
}

// AutoQuery 自动查询方法
func (ctrl *BaseAutoController[T]) AutoQuery(c *gin.Context, requestType reflect.Type) {
	reqPtr := reflect.New(requestType)
	req := reqPtr.Interface()

	if err := utils.BindAndValidate(c, req, "query"); err != nil {
		return
	}

	pageResp, err := ctrl.paginationService.QueryWithPagination(context.Background(), req)
	if err != nil {
		resp := response.InternalError("查询失败: " + err.Error())
		c.JSON(resp.GetHTTPStatus(), resp)
		return
	}

	resp := response.SuccessWithMessage(pageResp, "查询成功")
	c.JSON(resp.GetHTTPStatus(), resp)
}

// AutoGetByID 自动获取详情
func (ctrl *BaseAutoController[T]) AutoGetByID(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		resp := response.BadRequest("ID不能为空")
		c.JSON(resp.GetHTTPStatus(), resp)
		return
	}

	result, err := utils.FindByID[T](ctrl.collection, id)
	if err != nil {
		resp := response.NotFound("记录不存在")
		c.JSON(resp.GetHTTPStatus(), resp)
		return
	}

	resp := response.Success(result)
	c.JSON(resp.GetHTTPStatus(), resp)
}

// AutoUpdate 自动更新
func (ctrl *BaseAutoController[T]) AutoUpdate(c *gin.Context, requestType reflect.Type) {
	id := c.Param("id")
	if id == "" {
		resp := response.BadRequest("ID不能为空")
		c.JSON(resp.GetHTTPStatus(), resp)
		return
	}

	reqPtr := reflect.New(requestType)
	req := reqPtr.Interface()

	if err := utils.BindAndValidate(c, req, "json"); err != nil {
		return
	}

	err := utils.UpdateByID[T](ctrl.collection, id, req)
	if err != nil {
		resp := response.InternalError("更新失败: " + err.Error())
		c.JSON(resp.GetHTTPStatus(), resp)
		return
	}

	resp := response.SuccessWithMessage(nil, "更新成功")
	c.JSON(resp.GetHTTPStatus(), resp)
}

// AutoDelete 自动删除
func (ctrl *BaseAutoController[T]) AutoDelete(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		resp := response.BadRequest("ID不能为空")
		c.JSON(resp.GetHTTPStatus(), resp)
		return
	}

	err := utils.DeleteByID[T](ctrl.collection, id)
	if err != nil {
		resp := response.InternalError("删除失败: " + err.Error())
		c.JSON(resp.GetHTTPStatus(), resp)
		return
	}

	resp := response.SuccessWithMessage(nil, "删除成功")
	c.JSON(resp.GetHTTPStatus(), resp)
}

// ==================== Task 注解控制器实现 ====================

// TaskAnnotationController 任务注解控制器
type TaskAnnotationController struct {
	*BaseAutoController[model.Task]
}

// TaskCreateRequest 任务创建请求
type TaskCreateRequest struct {
	Name    string `json:"name" validate:"required,min=1,max=100" crud:"create"`
	Content string `json:"content" validate:"max=1000" crud:"create"`
}

// ToModel 转换为模型
func (req *TaskCreateRequest) ToModel() *model.Task {
	task := &model.Task{
		Name:    req.Name,
		Content: req.Content,
	}
	task.DefaultCreatedAt()
	task.DefaultUpdatedAt()
	return task
}

// TaskUpdateRequest 任务更新请求
type TaskUpdateRequest struct {
	Name    string `json:"name,omitempty" validate:"omitempty,min=1,max=100" crud:"update"`
	Content string `json:"content,omitempty" validate:"omitempty,max=1000" crud:"update"`
}

// TaskQueryRequest 任务查询请求
type TaskQueryRequest struct {
	Name__like    string `form:"name" validate:"omitempty,min=1,max=100" crud:"query"`
	Content__like string `form:"content" validate:"omitempty,max=1000" crud:"query"`
	Page          int    `form:"page" validate:"omitempty,min=1" crud:"query"`
	Size          int    `form:"size" validate:"omitempty,min=1,max=100" crud:"query"`
	Order         string `form:"order" validate:"omitempty" crud:"query"`
}

// NewTaskAnnotationController 创建任务注解控制器
func NewTaskAnnotationController() *TaskAnnotationController {
	config := AutoController{
		Collection: "task",
		Routes: map[string]RouteConfig{
			"create": {Method: "POST", Path: "/task", Handler: "Create", Business: false},
			"query":  {Method: "GET", Path: "/task/query", Handler: "Query", Business: false},
			"get":    {Method: "GET", Path: "/task/:id", Handler: "GetByID", Business: false},
			"update": {Method: "PUT", Path: "/task/:id", Handler: "Update", Business: false},
			"delete": {Method: "DELETE", Path: "/task/:id", Handler: "Delete", Business: false},
		},
	}

	return &TaskAnnotationController{
		BaseAutoController: NewBaseAutoController[model.Task](config),
	}
}

// Create 创建任务
func (tc *TaskAnnotationController) Create(c *gin.Context) {
	tc.AutoCreate(c, reflect.TypeOf(TaskCreateRequest{}))
}

// Query 查询任务
func (tc *TaskAnnotationController) Query(c *gin.Context) {
	tc.AutoQuery(c, reflect.TypeOf(TaskQueryRequest{}))
}

// GetByID 获取任务详情
func (tc *TaskAnnotationController) GetByID(c *gin.Context) {
	tc.AutoGetByID(c)
}

// Update 更新任务
func (tc *TaskAnnotationController) Update(c *gin.Context) {
	tc.AutoUpdate(c, reflect.TypeOf(TaskUpdateRequest{}))
}

// Delete 删除任务
func (tc *TaskAnnotationController) Delete(c *gin.Context) {
	tc.AutoDelete(c)
}

// ==================== 自动路由注册 ====================

// RegisterAutoRoutes 自动注册路由
func RegisterAutoRoutes(ctrl *TaskAnnotationController) {
	for _, route := range ctrl.config.Routes {
		switch route.Handler {
		case "Create":
			router.Api.Handle(route.Method, route.Path, ctrl.Create)
		case "Query":
			router.Api.Handle(route.Method, route.Path, ctrl.Query)
		case "GetByID":
			router.Api.Handle(route.Method, route.Path, ctrl.GetByID)
		case "Update":
			router.Api.Handle(route.Method, route.Path, ctrl.Update)
		case "Delete":
			router.Api.Handle(route.Method, route.Path, ctrl.Delete)
		}
	}
}

// 自动注册
var taskAnnotationController = NewTaskAnnotationController()

func init() {
	RegisterAutoRoutes(taskAnnotationController)
}
