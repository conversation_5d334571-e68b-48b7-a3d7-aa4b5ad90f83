# 配置驱动的控制器定义
controllers:
  task:
    collection: "task"
    model: "model.Task"
    
    # 请求结构定义
    requests:
      create:
        fields:
          - name: "Name"
            type: "string"
            json: "name"
            validate: "required,min=1,max=100"
          - name: "Content"
            type: "string"
            json: "content"
            validate: "max=1000"
      
      update:
        fields:
          - name: "Name"
            type: "string"
            json: "name,omitempty"
            validate: "omitempty,min=1,max=100"
          - name: "Content"
            type: "string"
            json: "content,omitempty"
            validate: "omitempty,max=1000"
      
      query:
        fields:
          - name: "Name__like"
            type: "string"
            form: "name"
            validate: "omitempty,min=1,max=100"
          - name: "Content__like"
            type: "string"
            form: "content"
            validate: "omitempty,max=1000"
          - name: "Page"
            type: "int"
            form: "page"
            validate: "omitempty,min=1"
          - name: "Size"
            type: "int"
            form: "size"
            validate: "omitempty,min=1,max=100"
    
    # 路由定义
    routes:
      - method: "POST"
        path: "/task"
        handler: "Create"
        request: "create"
        business: false
      
      - method: "GET"
        path: "/task/query"
        handler: "Query"
        request: "query"
        business: false
      
      - method: "GET"
        path: "/task/:id"
        handler: "GetByID"
        business: false
      
      - method: "PUT"
        path: "/task/:id"
        handler: "Update"
        request: "update"
        business: false
      
      - method: "DELETE"
        path: "/task/:id"
        handler: "Delete"
        business: false
    
    # 业务逻辑配置
    business:
      validators:
        - name: "NameNotEmpty"
          field: "Name"
          rule: "not_empty"
        - name: "ContentLength"
          field: "Content"
          rule: "max_length:1000"
      
      hooks:
        before_create:
          - "TrimSpaces"
          - "SetTimestamps"
        before_update:
          - "TrimSpaces"
          - "UpdateTimestamp"

  user:
    collection: "user"
    model: "model.User"
    
    requests:
      create:
        fields:
          - name: "Username"
            type: "string"
            json: "username"
            validate: "required,min=3,max=50"
          - name: "Email"
            type: "string"
            json: "email"
            validate: "required,email"
          - name: "Password"
            type: "string"
            json: "password"
            validate: "required,min=8"
      
      login:
        fields:
          - name: "Email"
            type: "string"
            json: "email"
            validate: "required,email"
          - name: "Password"
            type: "string"
            json: "password"
            validate: "required"
    
    routes:
      - method: "POST"
        path: "/user"
        handler: "Create"
        request: "create"
        business: true  # 需要复杂业务逻辑
      
      - method: "POST"
        path: "/user/login"
        handler: "Login"
        request: "login"
        business: true
    
    business:
      validators:
        - name: "EmailUnique"
          field: "Email"
          rule: "unique"
        - name: "UsernameUnique"
          field: "Username"
          rule: "unique"
      
      hooks:
        before_create:
          - "HashPassword"
          - "CheckEmailExists"
          - "CheckUsernameExists"
        
        custom_handlers:
          Login:
            steps:
              - "ValidateCredentials"
              - "GenerateToken"
              - "UpdateLastLogin"

# 全局配置
global:
  response:
    success_message: "操作成功"
    error_message: "操作失败"
  
  pagination:
    default_page: 1
    default_size: 10
    max_size: 100
  
  validation:
    required_message: "字段不能为空"
    email_message: "邮箱格式不正确"
    min_length_message: "长度不能少于{min}个字符"
    max_length_message: "长度不能超过{max}个字符"
