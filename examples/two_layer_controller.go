package examples

import (
	"context"
	"demo/database"
	"demo/model"
	"demo/response"
	"demo/utils"
	"fmt"
	"regexp"
	"time"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/v2/bson"
	"golang.org/x/crypto/bcrypt"
)

// TwoLayerUserController 两层架构示例：Controller直接调用Database
type TwoLayerUserController struct {
	repository *database.Repository[model.User]
}

func NewTwoLayerUserController() *TwoLayerUserController {
	return &TwoLayerUserController{
		repository: database.NewRepository[model.User]("user"),
	}
}

// ==================== 用户创建 - 两层架构 ====================

// CreateUserRequest 只需要定义一次
type CreateUserRequest struct {
	Username string `json:"username" validate:"required,min=3,max=50"`
	Email    string `json:"email" validate:"required,email"`
	Password string `json:"password" validate:"required,min=8"`
}

// CreateUser 创建用户 - 两层架构
func (uc *TwoLayerUserController) CreateUser(c *gin.Context) {
	var req CreateUserRequest

	// 1. HTTP参数绑定和验证
	if err := utils.BindAndValidate(c, &req, "json"); err != nil {
		return
	}

	// 2. 业务验证（直接在Controller中）
	if !uc.isValidEmail(req.Email) {
		resp := response.BadRequest("邮箱格式不正确")
		c.JSON(resp.GetHTTPStatus(), resp)
		return
	}

	// 3. 检查用户名是否已存在
	existingUser, _ := uc.repository.FindOne(context.Background(), bson.M{"username": req.Username})
	if existingUser != nil {
		resp := response.BadRequest("用户名已存在")
		c.JSON(resp.GetHTTPStatus(), resp)
		return
	}

	// 4. 检查邮箱是否已存在
	existingEmail, _ := uc.repository.FindOne(context.Background(), bson.M{"email": req.Email})
	if existingEmail != nil {
		resp := response.BadRequest("邮箱已被注册")
		c.JSON(resp.GetHTTPStatus(), resp)
		return
	}

	// 5. 密码加密
	hashedPassword, err := uc.hashPassword(req.Password)
	if err != nil {
		resp := response.InternalError("密码加密失败")
		c.JSON(resp.GetHTTPStatus(), resp)
		return
	}

	// 6. 创建用户对象（直接使用请求数据）
	user := &model.User{
		Username: req.Username,
		Email:    req.Email,
		Password: hashedPassword,
		Status:   "active",
	}
	user.DefaultCreatedAt()
	user.DefaultUpdatedAt()

	// 7. 直接调用数据层
	createdUser, err := uc.repository.Create(context.Background(), user)
	if err != nil {
		resp := response.InternalError("创建用户失败: " + err.Error())
		c.JSON(resp.GetHTTPStatus(), resp)
		return
	}

	// 8. 返回结果（清除敏感信息）
	createdUser.Password = ""
	resp := response.SuccessWithMessage(createdUser, "用户创建成功")
	c.JSON(resp.GetHTTPStatus(), resp)
}

// ==================== 辅助方法 ====================

func (uc *TwoLayerUserController) isValidEmail(email string) bool {
	emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	return emailRegex.MatchString(email)
}

func (uc *TwoLayerUserController) hashPassword(password string) (string, error) {
	hashedBytes, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return "", err
	}
	return string(hashedBytes), nil
}

// ==================== 查询示例 - 两层架构 ====================

type UserQueryRequest struct {
	Username__like string `form:"username" validate:"omitempty,min=1,max=50"`
	Email__like    string `form:"email" validate:"omitempty,email"`
	Status__eq     string `form:"status" validate:"omitempty,oneof=active inactive"`
	Page           int    `form:"page" validate:"omitempty,min=1"`
	Size           int    `form:"size" validate:"omitempty,min=1,max=100"`
	Order          string `form:"order" validate:"omitempty"`
}

// QueryUsers 查询用户 - 两层架构
func (uc *TwoLayerUserController) QueryUsers(c *gin.Context) {
	var req UserQueryRequest

	// 1. 参数绑定和验证
	if err := utils.BindAndValidate(c, &req, "query"); err != nil {
		return
	}

	// 2. 构建查询条件（直接在Controller中）
	queryBuilder := database.NewQueryBuilder()
	queryMap := utils.StructToMap(req)
	query := queryBuilder.BuildFromMap(queryMap)

	if len(queryBuilder.GetErrors()) > 0 {
		resp := response.BadRequest(fmt.Sprintf("查询参数错误: %v", queryBuilder.GetErrors()))
		c.JSON(resp.GetHTTPStatus(), resp)
		return
	}

	// 3. 构建分页参数
	page := req.Page
	if page <= 0 {
		page = 1
	}
	size := req.Size
	if size <= 0 {
		size = 10
	}

	skip := int64((page - 1) * size)
	limit := int64(size)

	// 4. 构建排序
	var sortBson bson.D
	if req.Order != "" {
		// 简化排序处理
		sortBson = bson.D{{Key: "created_at", Value: -1}}
	} else {
		sortBson = bson.D{{Key: "created_at", Value: -1}}
	}

	// 5. 直接调用数据层
	filter := uc.bsonDToM(query.Build())
	results, total, err := uc.repository.FindWithPagination(context.Background(), filter, skip, limit, sortBson)
	if err != nil {
		resp := response.InternalError("查询失败: " + err.Error())
		c.JSON(resp.GetHTTPStatus(), resp)
		return
	}

	// 6. 清除敏感信息
	for i := range results {
		results[i].Password = ""
	}

	// 7. 构建分页响应
	pageResp := response.NewPageResponse(results, total, int64(page), int64(size))
	resp := response.SuccessWithMessage(pageResp, "查询成功")
	c.JSON(resp.GetHTTPStatus(), resp)
}

// bsonDToM 将 bson.D 转换为 bson.M
func (uc *TwoLayerUserController) bsonDToM(d bson.D) bson.M {
	m := make(bson.M)
	for _, elem := range d {
		m[elem.Key] = elem.Value
	}
	return m
}
