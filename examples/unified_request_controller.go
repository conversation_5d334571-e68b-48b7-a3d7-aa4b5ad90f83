package examples

import (
	"context"
	"demo/database"
	"demo/model"
	"demo/response"
	"demo/utils"

	"github.com/gin-gonic/gin"
)

// ==================== 统一请求接口 ====================

// RequestConverter 请求转换接口
type RequestConverter[T any] interface {
	ToModel() *T
	Validate() error
}

// ServiceRequest 服务层请求接口
type ServiceRequest interface {
	Validate() error
}

// ==================== 统一的CRUD控制器 ====================

// CRUDController 通用CRUD控制器
type CRUDController[T any, CreateReq RequestConverter[T], UpdateReq RequestConverter[T]] struct {
	repository *database.Repository[T]
	collection string
}

// NewCRUDController 创建CRUD控制器
func NewCRUDController[T any, CreateReq RequestConverter[T], UpdateReq RequestConverter[T]](collection string) *CRUDController[T, CreateReq, UpdateReq] {
	return &CRUDController[T, CreateReq, UpdateReq]{
		repository: database.NewRepository[T](collection),
		collection: collection,
	}
}

// Create 通用创建方法
func (ctrl *CRUDController[T, CreateReq, UpdateReq]) Create(c *gin.Context) {
	var req CreateReq

	if err := utils.BindAndValidate(c, &req, "json"); err != nil {
		return
	}

	// 转换为模型
	model := req.ToModel()

	// 调用数据层
	result, err := ctrl.repository.Create(context.Background(), model)
	if err != nil {
		resp := response.InternalError("创建失败: " + err.Error())
		c.JSON(resp.GetHTTPStatus(), resp)
		return
	}

	resp := response.SuccessWithMessage(result, "创建成功")
	c.JSON(resp.GetHTTPStatus(), resp)
}

// Update 通用更新方法
func (ctrl *CRUDController[T, CreateReq, UpdateReq]) Update(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		resp := response.BadRequest("ID不能为空")
		c.JSON(resp.GetHTTPStatus(), resp)
		return
	}

	var req UpdateReq
	if err := utils.BindAndValidate(c, &req, "json"); err != nil {
		return
	}

	// 直接使用工具函数更新
	err := utils.UpdateByID[T](ctrl.collection, id, req)
	if err != nil {
		resp := response.InternalError("更新失败: " + err.Error())
		c.JSON(resp.GetHTTPStatus(), resp)
		return
	}

	resp := response.SuccessWithMessage(nil, "更新成功")
	c.JSON(resp.GetHTTPStatus(), resp)
}

// GetByID 通用获取方法
func (ctrl *CRUDController[T, CreateReq, UpdateReq]) GetByID(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		resp := response.BadRequest("ID不能为空")
		c.JSON(resp.GetHTTPStatus(), resp)
		return
	}

	result, err := utils.FindByID[T](ctrl.collection, id)
	if err != nil {
		resp := response.NotFound("记录不存在")
		c.JSON(resp.GetHTTPStatus(), resp)
		return
	}

	resp := response.Success(result)
	c.JSON(resp.GetHTTPStatus(), resp)
}

// Delete 通用删除方法
func (ctrl *CRUDController[T, CreateReq, UpdateReq]) Delete(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		resp := response.BadRequest("ID不能为空")
		c.JSON(resp.GetHTTPStatus(), resp)
		return
	}

	err := utils.DeleteByID[T](ctrl.collection, id)
	if err != nil {
		resp := response.InternalError("删除失败: " + err.Error())
		c.JSON(resp.GetHTTPStatus(), resp)
		return
	}

	resp := response.SuccessWithMessage(nil, "删除成功")
	c.JSON(resp.GetHTTPStatus(), resp)
}

// ==================== Task 具体实现 ====================

// TaskCreateRequest 任务创建请求
type TaskCreateRequest struct {
	Name    string `json:"name" validate:"required,min=1,max=100"`
	Content string `json:"content" validate:"max=1000"`
}

// ToModel 转换为模型
func (req TaskCreateRequest) ToModel() *model.Task {
	task := &model.Task{
		Name:    req.Name,
		Content: req.Content,
	}
	task.DefaultCreatedAt()
	task.DefaultUpdatedAt()
	return task
}

// Validate 验证请求
func (req TaskCreateRequest) Validate() error {
	// 可以添加额外的业务验证
	return nil
}

// TaskUpdateRequest 任务更新请求
type TaskUpdateRequest struct {
	Name    string `json:"name,omitempty" validate:"omitempty,min=1,max=100"`
	Content string `json:"content,omitempty" validate:"omitempty,max=1000"`
}

// ToModel 转换为模型
func (req TaskUpdateRequest) ToModel() *model.Task {
	task := &model.Task{
		Name:    req.Name,
		Content: req.Content,
	}
	task.DefaultUpdatedAt()
	return task
}

// Validate 验证请求
func (req TaskUpdateRequest) Validate() error {
	return nil
}

// TaskQueryRequest 任务查询请求
type TaskQueryRequest struct {
	Name__like    string `form:"name" validate:"omitempty,min=1,max=100"`
	Content__like string `form:"content" validate:"omitempty,max=1000"`
	Page          int    `form:"page" validate:"omitempty,min=1"`
	Size          int    `form:"size" validate:"omitempty,min=1,max=100"`
	Order         string `form:"order" validate:"omitempty"`
}

// ==================== Task 控制器 ====================

// TaskController 任务控制器
type TaskController struct {
	*CRUDController[model.Task, TaskCreateRequest, TaskUpdateRequest]
	paginationService *service.PaginationService[model.Task]
}

// NewTaskController 创建任务控制器
func NewTaskController() *TaskController {
	return &TaskController{
		CRUDController:    NewCRUDController[model.Task, TaskCreateRequest, TaskUpdateRequest]("task"),
		paginationService: service.NewPaginationService[model.Task]("task"),
	}
}

// Query 查询任务列表
func (tc *TaskController) Query(c *gin.Context) {
	var req TaskQueryRequest

	if err := utils.BindAndValidate(c, &req, "query"); err != nil {
		return
	}

	// 使用分页服务
	pageResp, err := tc.paginationService.QueryWithPagination(context.Background(), req)
	if err != nil {
		resp := response.InternalError("查询失败: " + err.Error())
		c.JSON(resp.GetHTTPStatus(), resp)
		return
	}

	resp := response.SuccessWithMessage(pageResp, "查询成功")
	c.JSON(resp.GetHTTPStatus(), resp)
}

// ==================== 路由注册 ====================

var taskController = NewTaskController()

func init() {
	// 使用统一的CRUD方法
	router.Api.POST("/task", taskController.Create)
	router.Api.GET("/task/:id", taskController.GetByID)
	router.Api.PUT("/task/:id", taskController.Update)
	router.Api.DELETE("/task/:id", taskController.Delete)
	router.Api.GET("/task/query", taskController.Query)
}
