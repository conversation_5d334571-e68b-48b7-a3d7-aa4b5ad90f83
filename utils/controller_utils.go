package utils

import (
	"context"
	"demo/database"
	"demo/model"
	"demo/response"
	"demo/validator"
	"fmt"
	"net/http"
	"reflect"
	"regexp"
	"strings"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/v2/bson"
)

// ==================== 参数绑定和验证 ====================

// BindAndValidate 统一的参数绑定和验证
func BindAndValidate(c *gin.Context, obj interface{}, bindType string) error {
	var err error

	switch bindType {
	case "json":
		err = c.ShouldBindJ<PERSON>N(obj)
	case "query":
		err = c.<PERSON><PERSON>y(obj)
	case "form":
		err = c.ShouldBind(obj)
	case "uri":
		err = c.ShouldBindUri(obj)
	case "both":
		// 先绑定 URI 参数，再绑定 JSON 参数
		if err = c.ShouldBind<PERSON>ri(obj); err != nil {
			break
		}
		err = c.ShouldBindJSON(obj)
	}

	if err != nil {
		resp := response.Fail("参数绑定失败: " + err.Error())
		c.JSON(http.StatusBadRequest, resp)
		return err
	}

	// 验证参数
	if err := validator.Validate(obj); err != nil {
		validationResp := validator.NewValidationErrorResponse(err)
		c.JSON(http.StatusBadRequest, validationResp)
		return err
	}

	return nil
}

// ==================== 数据库操作辅助 ====================
// 注意：这些函数使用新的 repository 层，保持简洁的接口

// CreateEntity 通用创建实体
func CreateEntity[T any](collectionName string, entity *T) (*T, error) {
	repository := database.NewRepository[T](collectionName)
	return repository.Create(context.Background(), entity)
}

// UpdateEntity 通用更新实体
func UpdateEntity(collectionName string, id string, updateData interface{}) error {
	repository := database.NewRepository[model.Task](collectionName)

	queryBuilder := database.NewQueryBuilder()
	filter := queryBuilder.BuildFromMap(map[string]interface{}{"id": id})
	updateDoc := structToBsonM(updateData)
	delete(updateDoc, "_id")

	matchedCount, err := repository.UpdateByFilter(context.Background(), bsonDToM(filter.Build()), updateDoc)
	if err != nil {
		return err
	}

	if matchedCount == 0 {
		return fmt.Errorf("entity not found")
	}

	return nil
}

// DeleteEntity 通用删除实体
func DeleteEntity(collectionName string, id string) error {
	repository := database.NewRepository[model.Task](collectionName)

	queryBuilder := database.NewQueryBuilder()
	filter := queryBuilder.BuildFromMap(map[string]interface{}{"id": id})

	deletedCount, err := repository.DeleteByFilter(context.Background(), filter.Build())
	if err != nil {
		return err
	}

	if deletedCount == 0 {
		return fmt.Errorf("entity not found")
	}

	return nil
}

// GetEntityByID 通用根据ID获取实体
func GetEntityByID[T any](collectionName string, id string) (*T, error) {
	repository := database.NewRepository[T](collectionName)

	queryBuilder := database.NewQueryBuilder()
	filter := queryBuilder.BuildFromMap(map[string]interface{}{"id": id})

	return repository.FindOne(context.Background(), filter.Build())
}

// ==================== 请求ID处理 ====================

// GetRequestID 获取请求ID
func GetRequestID(c *gin.Context) string {
	if requestID, exists := c.Get("request_id"); exists {
		return requestID.(string)
	}
	return ""
}

// ==================== 数据转换辅助 ====================

// StructToMap 将结构体转换为 map[string]interface{}
func StructToMap(obj interface{}) map[string]interface{} {
	return structToBsonM(obj)
}

// structToBsonM 将结构体转换为 bson.M
func structToBsonM(data interface{}) bson.M {
	result := bson.M{}
	dataValue := reflect.ValueOf(data)
	dataType := reflect.TypeOf(data)

	if dataValue.Kind() == reflect.Ptr {
		dataValue = dataValue.Elem()
		dataType = dataType.Elem()
	}

	for i := 0; i < dataValue.NumField(); i++ {
		field := dataType.Field(i)
		fieldValue := dataValue.Field(i).Interface()

		// 获取字段名
		fieldName := getFieldName(field)

		// 处理字段值
		if fieldValue != nil && !isZeroValue(fieldValue) {
			result[fieldName] = fieldValue
		}
	}

	return result
}

// getFieldName 获取字段名（支持 form 标签）
func getFieldName(field reflect.StructField) string {
	// 优先使用 form 标签
	if formTag := field.Tag.Get("form"); formTag != "" {
		return formTag
	}

	// 其次使用 bson 标签
	if bsonTag := field.Tag.Get("bson"); bsonTag != "" {
		tagParts := strings.Split(bsonTag, ",")
		if tagParts[0] != "" {
			return tagParts[0]
		}
	}

	// 最后使用驼峰转下划线
	return camelToSnake(field.Name)
}

// camelToSnake 将驼峰命名转换为下划线命名
func camelToSnake(s string) string {
	s = regexp.MustCompile("([a-z])([A-Z])").ReplaceAllString(s, "${1}_${2}")
	return strings.ToLower(s)
}

// isZeroValue 检查是否为零值
func isZeroValue(v interface{}) bool {
	if v == nil {
		return true
	}

	switch val := v.(type) {
	case string:
		return val == ""
	case int, int8, int16, int32, int64:
		return val == 0
	case uint, uint8, uint16, uint32, uint64:
		return val == 0
	case float32, float64:
		return val == 0
	case bool:
		return !val
	default:
		return false
	}
}
