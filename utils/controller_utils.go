package utils

import (
	"context"
	"demo/bak"
	"demo/database"
	"demo/model"
	"demo/response"
	"demo/validator"
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
)

// ==================== 参数绑定和验证 ====================

// BindAndValidate 统一的参数绑定和验证
func BindAndValidate(c *gin.Context, obj interface{}, bindType string) error {
	var err error

	switch bindType {
	case "json":
		err = c.ShouldBindJSON(obj)
	case "query":
		err = c.ShouldBindQuery(obj)
	case "form":
		err = c.ShouldBind(obj)
	case "uri":
		err = c.ShouldBindUri(obj)
	}

	if err != nil {
		resp := response.Fail("参数绑定失败: " + err.Error())
		c.JSON(http.StatusBadRequest, resp)
		return err
	}

	// 验证参数
	if err := validator.Validate(obj); err != nil {
		validationResp := validator.NewValidationErrorResponse(err)
		c.JSON(http.StatusBadRequest, validationResp)
		return err
	}

	return nil
}

// ==================== 数据库操作辅助 ====================
// 注意：这些函数使用新的 repository 层，保持简洁的接口

// CreateEntity 通用创建实体
func CreateEntity[T any](collectionName string, entity *T) (*T, error) {
	repository := database.NewRepository[T](collectionName)
	return repository.Create(context.Background(), entity)
}

// UpdateEntity 通用更新实体
func UpdateEntity(collectionName string, id string, updateData interface{}) error {
	repository := database.NewRepository[model.Task](collectionName)

	queryBuilder := database.NewQueryBuilder()
	filter := queryBuilder.BuildFromMap(map[string]interface{}{"id": id})
	updateDoc := bak.StructToBsonM(updateData)
	delete(updateDoc, "_id")

	matchedCount, err := repository.UpdateByFilter(context.Background(), filter.Build(), updateDoc)
	if err != nil {
		return err
	}

	if matchedCount == 0 {
		return fmt.Errorf("entity not found")
	}

	return nil
}

// DeleteEntity 通用删除实体
func DeleteEntity(collectionName string, id string) error {
	repository := database.NewRepository[model.Task](collectionName)

	queryBuilder := database.NewQueryBuilder()
	filter := queryBuilder.BuildFromMap(map[string]interface{}{"id": id})

	deletedCount, err := repository.DeleteByFilter(context.Background(), filter.Build())
	if err != nil {
		return err
	}

	if deletedCount == 0 {
		return fmt.Errorf("entity not found")
	}

	return nil
}

// GetEntityByID 通用根据ID获取实体
func GetEntityByID[T any](collectionName string, id string) (*T, error) {
	repository := database.NewRepository[T](collectionName)

	queryBuilder := database.NewQueryBuilder()
	filter := queryBuilder.BuildFromMap(map[string]interface{}{"id": id})

	return repository.FindOne(context.Background(), filter.Build())
}

// ==================== 请求ID处理 ====================

// GetRequestID 获取请求ID
func GetRequestID(c *gin.Context) string {
	if requestID, exists := c.Get("request_id"); exists {
		return requestID.(string)
	}
	return ""
}

// ==================== 数据转换辅助 ====================

// StructToMap 将结构体转换为 map[string]interface{}
func StructToMap(obj interface{}) map[string]interface{} {
	return bak.StructToBsonM(obj)
}
