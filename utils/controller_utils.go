package utils

import (
	"context"
	"demo/database"
	"demo/model"
	"demo/response"
	"demo/validator"
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
)

// ==================== 参数绑定和验证 ====================

// BindAndValidate 统一的参数绑定和验证
func BindAndValidate(c *gin.Context, obj interface{}, bindType string) error {
	var err error
	
	switch bindType {
	case "json":
		err = c.ShouldBindJSON(obj)
	case "query":
		err = c.ShouldBindQuery(obj)
	case "form":
		err = c.ShouldBind(obj)
	case "uri":
		err = c.ShouldBindUri(obj)
	}
	
	if err != nil {
		resp := response.Fail("参数绑定失败: " + err.Error())
		c.JSON(http.StatusBadRequest, resp)
		return err
	}

	// 验证参数
	if err := validator.Validate(obj); err != nil {
		validationResp := validator.NewValidationErrorResponse(err)
		c.JSON(http.StatusBadRequest, validationResp)
		return err
	}

	return nil
}

// ==================== 响应处理 ====================

// RespondError 统一错误响应
func RespondError(c *gin.Context, message string, err error) {
	resp := response.Fail(message + ": " + err.Error())
	c.JSON(http.StatusInternalServerError, resp)
}

// RespondSuccess 统一成功响应
func RespondSuccess(c *gin.Context, data interface{}, message string) {
	resp := response.SuccessWithMessage(data, message)
	c.JSON(http.StatusOK, resp)
}

// RespondNotFound 404响应
func RespondNotFound(c *gin.Context, message string) {
	resp := response.NotFound(message)
	c.JSON(http.StatusNotFound, resp)
}

// RespondBadRequest 400响应
func RespondBadRequest(c *gin.Context, message string) {
	resp := response.Fail(message)
	c.JSON(http.StatusBadRequest, resp)
}

// ==================== 数据库操作辅助 ====================

// QueryWithPagination 通用分页查询
func QueryWithPagination[T any](collectionName string, params interface{}) (*response.PagedResponse, error) {
	paginationService := database.NewPaginationService[T](collectionName)
	queryParams := database.StructToBsonM(params) // 保持原来的便利方法
	return paginationService.QueryWithPagination(context.Background(), queryParams)
}

// CreateEntity 通用创建实体
func CreateEntity[T any](collectionName string, entity *T) (*T, error) {
	collection := database.GetCollection[T](collectionName)
	return collection.Creator().InsertOne(context.Background(), entity)
}

// UpdateEntity 通用更新实体
func UpdateEntity(collectionName string, id string, updateData interface{}) error {
	collection := database.GetCollection[model.Task](collectionName)
	
	queryBuilder := database.NewQueryBuilder()
	filter := queryBuilder.BuildFromMap(map[string]interface{}{"id": id})
	updateDoc := database.StructToBsonM(updateData)
	delete(updateDoc, "_id")

	result, err := collection.Updater().Filter(filter.Build()).Updates(updateDoc).UpdateOne(context.Background())
	if err != nil {
		return err
	}
	
	if result.MatchedCount == 0 {
		return fmt.Errorf("entity not found")
	}
	
	return nil
}

// DeleteEntity 通用删除实体
func DeleteEntity(collectionName string, id string) error {
	collection := database.GetCollection[model.Task](collectionName)
	
	queryBuilder := database.NewQueryBuilder()
	filter := queryBuilder.BuildFromMap(map[string]interface{}{"id": id})

	result, err := collection.Deleter().Filter(filter.Build()).DeleteOne(context.Background())
	if err != nil {
		return err
	}
	
	if result.DeletedCount == 0 {
		return fmt.Errorf("entity not found")
	}
	
	return nil
}

// GetEntityByID 通用根据ID获取实体
func GetEntityByID[T any](collectionName string, id string) (*T, error) {
	collection := database.GetCollection[T](collectionName)
	
	queryBuilder := database.NewQueryBuilder()
	filter := queryBuilder.BuildFromMap(map[string]interface{}{"id": id})

	return collection.Finder().Filter(filter.Build()).FindOne(context.Background())
}

// ==================== 请求ID处理 ====================

// GetRequestID 获取请求ID
func GetRequestID(c *gin.Context) string {
	if requestID, exists := c.Get("request_id"); exists {
		return requestID.(string)
	}
	return ""
}
