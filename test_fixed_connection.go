package main

import (
	"context"
	"demo/config"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/v2/bson"
	"go.mongodb.org/mongo-driver/v2/mongo"
	"go.mongodb.org/mongo-driver/v2/mongo/options"
)

func main() {
	fmt.Println("测试修复后的MongoDB连接...")
	
	cfg := config.AppConfig.MongoDB
	fmt.Printf("连接到: %s:%d\n", cfg.Host, cfg.Port)
	fmt.Printf("数据库: %s\n", cfg.DBName)
	fmt.Printf("用户: %s\n", cfg.Username)
	
	// 使用调试工具中成功的连接方式
	uri := cfg.GetConnectionString()
	fmt.Printf("连接字符串: %s\n", uri)
	
	fmt.Println("\n开始连接...")
	start := time.Now()
	
	// 使用成功的连接参数
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	
	clientOptions := options.Client().ApplyURI(uri)
	clientOptions.SetConnectTimeout(30 * time.Second)
	clientOptions.SetServerSelectionTimeout(30 * time.Second)
	
	client, err := mongo.Connect(clientOptions)
	if err != nil {
		fmt.Printf("❌ 连接创建失败: %v\n", err)
		return
	}
	defer client.Disconnect(ctx)
	
	fmt.Printf("连接创建耗时: %v\n", time.Since(start))
	
	// 测试ping
	pingStart := time.Now()
	if err := client.Ping(ctx, nil); err != nil {
		fmt.Printf("❌ Ping失败: %v\n", err)
		return
	}
	
	fmt.Printf("✅ Ping成功! 耗时: %v\n", time.Since(pingStart))
	fmt.Printf("总连接时间: %v\n", time.Since(start))
	
	// 测试数据库操作
	fmt.Println("\n测试数据库操作...")
	
	// 列出数据库
	databases, err := client.ListDatabaseNames(ctx, bson.D{})
	if err != nil {
		fmt.Printf("❌ 列出数据库失败: %v\n", err)
		return
	}
	fmt.Printf("✅ 可用数据库: %v\n", databases)
	
	// 测试目标数据库
	db := client.Database(cfg.DBName)
	collections, err := db.ListCollectionNames(ctx, bson.D{})
	if err != nil {
		fmt.Printf("❌ 列出集合失败: %v\n", err)
		return
	}
	fmt.Printf("✅ 数据库 %s 中的集合: %v\n", cfg.DBName, collections)
	
	// 测试插入操作
	fmt.Println("\n测试插入操作...")
	collection := db.Collection("test_connection")
	
	testDoc := bson.D{
		{Key: "test", Value: "connection_test"},
		{Key: "timestamp", Value: time.Now()},
		{Key: "success", Value: true},
	}
	
	result, err := collection.InsertOne(ctx, testDoc)
	if err != nil {
		fmt.Printf("❌ 插入失败: %v\n", err)
		return
	}
	fmt.Printf("✅ 插入成功! ID: %v\n", result.InsertedID)
	
	// 测试查询操作
	var foundDoc bson.D
	err = collection.FindOne(ctx, bson.D{{Key: "test", Value: "connection_test"}}).Decode(&foundDoc)
	if err != nil {
		fmt.Printf("❌ 查询失败: %v\n", err)
		return
	}
	fmt.Printf("✅ 查询成功! 文档: %v\n", foundDoc)
	
	// 清理测试数据
	_, err = collection.DeleteOne(ctx, bson.D{{Key: "test", Value: "connection_test"}})
	if err != nil {
		fmt.Printf("⚠️ 清理失败: %v\n", err)
	} else {
		fmt.Printf("✅ 测试数据已清理\n")
	}
	
	fmt.Println("\n🎉 MongoDB连接测试完全成功!")
	fmt.Println("现在可以更新项目代码使用这些连接参数")
}
