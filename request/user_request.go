package request

import (
	"demo/model"
	"fmt"
	"strings"
	"time"
)

// ==================== 用户相关请求结构体 ====================

// UserCreateRequest 用户创建请求
type UserCreateRequest struct {
	Username string `json:"username" validate:"required,min=3,max=50"`
	Email    string `json:"email" validate:"required,email"`
	Password string `json:"password" validate:"required,min=8"`
}

// ToModel 转换为用户模型
func (req *UserCreateRequest) ToModel() *model.User {
	user := &model.User{
		Username: strings.TrimSpace(req.Username),
		Email:    strings.TrimSpace(req.Email),
		Password: req.Password, // 注意：实际使用时需要加密
		Status:   "active",
	}
	user.DefaultCreatedAt()
	user.DefaultUpdatedAt()
	return user
}

// Validate 自定义验证
func (req *UserCreateRequest) Validate() error {
	if strings.TrimSpace(req.Username) == "" {
		return fmt.Errorf("用户名不能为空")
	}
	if strings.TrimSpace(req.Email) == "" {
		return fmt.Errorf("邮箱不能为空")
	}
	return nil
}

// UserUpdateRequest 用户更新请求
type UserUpdateRequest struct {
	Username string `json:"username,omitempty" validate:"omitempty,min=3,max=50"`
	Email    string `json:"email,omitempty" validate:"omitempty,email"`
	Status   string `json:"status,omitempty" validate:"omitempty,oneof=active inactive"`
}

// ToUpdateMap 转换为更新映射
func (req *UserUpdateRequest) ToUpdateMap() map[string]interface{} {
	updateMap := make(map[string]interface{})
	
	if req.Username != "" {
		updateMap["username"] = strings.TrimSpace(req.Username)
	}
	if req.Email != "" {
		updateMap["email"] = strings.TrimSpace(req.Email)
	}
	if req.Status != "" {
		updateMap["status"] = req.Status
	}
	
	// 总是更新时间戳
	updateMap["updated_at"] = time.Now()
	
	return updateMap
}

// UserQueryRequest 用户查询请求
type UserQueryRequest struct {
	Username__like string `form:"username" validate:"omitempty,min=1,max=50"`
	Email__like    string `form:"email" validate:"omitempty,email"`
	Status__eq     string `form:"status" validate:"omitempty,oneof=active inactive"`
	Page           int    `form:"page" validate:"omitempty,min=1"`
	Size           int    `form:"size" validate:"omitempty,min=1,max=100"`
	Order          string `form:"order" validate:"omitempty"`
}

// GetPage 获取页码
func (req *UserQueryRequest) GetPage() int {
	if req.Page <= 0 {
		return 1
	}
	return req.Page
}

// GetSize 获取页大小
func (req *UserQueryRequest) GetSize() int {
	if req.Size <= 0 {
		return 10
	}
	if req.Size > 100 {
		return 100
	}
	return req.Size
}

// UserLoginRequest 用户登录请求
type UserLoginRequest struct {
	Email    string `json:"email" validate:"required,email"`
	Password string `json:"password" validate:"required"`
}

// UserChangePasswordRequest 修改密码请求
type UserChangePasswordRequest struct {
	OldPassword string `json:"old_password" validate:"required"`
	NewPassword string `json:"new_password" validate:"required,min=8"`
}

// UserResetPasswordRequest 重置密码请求
type UserResetPasswordRequest struct {
	Email string `json:"email" validate:"required,email"`
}

// UserGetByIDRequest 根据ID获取用户请求
type UserGetByIDRequest struct {
	ID string `uri:"id" validate:"required,objectid"`
}

// UserDeleteRequest 删除用户请求
type UserDeleteRequest struct {
	ID string `uri:"id" validate:"required,objectid"`
}
