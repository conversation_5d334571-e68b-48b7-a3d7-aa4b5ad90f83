package main

import (
	"context"
	"demo/config"
	"fmt"
	"net"
	"time"

	"go.mongodb.org/mongo-driver/v2/bson"
	"go.mongodb.org/mongo-driver/v2/mongo"
	"go.mongodb.org/mongo-driver/v2/mongo/options"
)

func main() {
	fmt.Println("=== MongoDB连接诊断工具 ===")
	
	// 1. 检查配置
	fmt.Println("\n1. 检查配置...")
	cfg := config.AppConfig.MongoDB
	fmt.Printf("  Host: %s\n", cfg.Host)
	fmt.Printf("  Port: %d\n", cfg.Port)
	fmt.Printf("  Database: %s\n", cfg.DBName)
	fmt.Printf("  Username: %s\n", cfg.Username)
	fmt.Printf("  Password: %s\n", maskPassword(cfg.Password))
	
	connectionString := cfg.GetConnectionString()
	fmt.Printf("  Connection String: %s\n", connectionString)
	
	// 2. 测试网络连接
	fmt.Println("\n2. 测试网络连接...")
	address := fmt.Sprintf("%s:%d", cfg.Host, cfg.Port)
	fmt.Printf("  尝试连接到: %s\n", address)
	
	conn, err := net.DialTimeout("tcp", address, 5*time.Second)
	if err != nil {
		fmt.Printf("  ❌ TCP连接失败: %v\n", err)
		fmt.Println("  可能的原因:")
		fmt.Println("    - MongoDB服务未启动")
		fmt.Println("    - 防火墙阻止连接")
		fmt.Println("    - 网络不通")
		fmt.Println("    - 端口配置错误")
		return
	}
	conn.Close()
	fmt.Printf("  ✅ TCP连接成功\n")
	
	// 3. 测试MongoDB连接（无认证）
	fmt.Println("\n3. 测试MongoDB连接（无认证）...")
	testMongoConnection(fmt.Sprintf("mongodb://%s:%d", cfg.Host, cfg.Port), "无认证")
	
	// 4. 测试MongoDB连接（有认证）
	fmt.Println("\n4. 测试MongoDB连接（有认证）...")
	testMongoConnection(connectionString, "有认证")
	
	// 5. 测试不同的连接选项
	fmt.Println("\n5. 测试不同的连接选项...")
	testWithDifferentOptions(cfg)
	
	// 6. 测试本地MongoDB
	fmt.Println("\n6. 测试本地MongoDB连接...")
	testMongoConnection("mongodb://localhost:27017", "本地默认")
	
	fmt.Println("\n=== 诊断完成 ===")
}

func testMongoConnection(uri, description string) {
	fmt.Printf("  测试: %s\n", description)
	fmt.Printf("  URI: %s\n", uri)
	
	// 创建客户端选项
	clientOptions := options.Client().ApplyURI(uri)
	clientOptions.SetConnectTimeout(5 * time.Second)
	clientOptions.SetServerSelectionTimeout(5 * time.Second)
	
	// 连接
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	
	client, err := mongo.Connect(clientOptions)
	if err != nil {
		fmt.Printf("  ❌ 连接创建失败: %v\n", err)
		return
	}
	defer client.Disconnect(ctx)
	
	// 测试ping
	if err := client.Ping(ctx, nil); err != nil {
		fmt.Printf("  ❌ Ping失败: %v\n", err)
		return
	}
	
	fmt.Printf("  ✅ 连接成功\n")
	
	// 尝试列出数据库
	databases, err := client.ListDatabaseNames(ctx, bson.D{})
	if err != nil {
		fmt.Printf("  ⚠️  列出数据库失败: %v\n", err)
	} else {
		fmt.Printf("  ✅ 可访问的数据库: %v\n", databases)
	}
}

func testWithDifferentOptions(cfg config.MongoDBConfig) {
	baseURI := fmt.Sprintf("mongodb://%s:%d", cfg.Host, cfg.Port)
	
	// 测试不同的选项组合
	testCases := []struct {
		name string
		uri  string
		opts func(*options.ClientOptions)
	}{
		{
			name: "基础连接",
			uri:  baseURI,
			opts: func(opts *options.ClientOptions) {
				opts.SetConnectTimeout(10 * time.Second)
			},
		},
		{
			name: "直接连接模式",
			uri:  baseURI,
			opts: func(opts *options.ClientOptions) {
				opts.SetConnectTimeout(10 * time.Second)
				opts.SetDirect(true)
			},
		},
		{
			name: "禁用SSL",
			uri:  baseURI + "?ssl=false",
			opts: func(opts *options.ClientOptions) {
				opts.SetConnectTimeout(10 * time.Second)
			},
		},
		{
			name: "认证到admin数据库",
			uri:  fmt.Sprintf("mongodb://%s:%s@%s:%d/admin", cfg.Username, cfg.Password, cfg.Host, cfg.Port),
			opts: func(opts *options.ClientOptions) {
				opts.SetConnectTimeout(10 * time.Second)
			},
		},
		{
			name: "认证到指定数据库",
			uri:  fmt.Sprintf("mongodb://%s:%s@%s:%d/%s", cfg.Username, cfg.Password, cfg.Host, cfg.Port, cfg.DBName),
			opts: func(opts *options.ClientOptions) {
				opts.SetConnectTimeout(10 * time.Second)
			},
		},
	}
	
	for _, tc := range testCases {
		fmt.Printf("  测试: %s\n", tc.name)
		
		clientOptions := options.Client().ApplyURI(tc.uri)
		tc.opts(clientOptions)
		
		ctx, cancel := context.WithTimeout(context.Background(), 15*time.Second)
		
		client, err := mongo.Connect(clientOptions)
		if err != nil {
			fmt.Printf("    ❌ 连接失败: %v\n", err)
			cancel()
			continue
		}
		
		if err := client.Ping(ctx, nil); err != nil {
			fmt.Printf("    ❌ Ping失败: %v\n", err)
		} else {
			fmt.Printf("    ✅ 连接成功\n")
		}
		
		client.Disconnect(ctx)
		cancel()
	}
}

func maskPassword(password string) string {
	if password == "" {
		return "(空)"
	}
	if len(password) <= 2 {
		return "***"
	}
	return password[:2] + "***"
}
