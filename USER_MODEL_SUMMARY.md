# 🚀 User 模型创建总结

## 📊 **User 模型设计**

### **🎯 设计目标**
根据 `user_service.go` 中的 `CreateUser` 方法和业务逻辑，创建完整的 User 模型，支持：
- 用户基本信息管理
- 密码安全处理
- 角色权限控制
- 登录状态跟踪
- 邮箱验证功能
- 账户锁定机制

## 🔧 **模型结构**

### **✅ 基础字段**
```go
type User struct {
    base.ModelBase `bson:",inline"`  // 继承基础模型（ID、CreatedAt、UpdatedAt、DeletedAt）
    
    // 基本信息
    Username string `json:"username" bson:"username" validate:"required,min=3,max=50"`
    Email    string `json:"email" bson:"email" validate:"required,email"`
    Password string `json:"password,omitempty" bson:"password" validate:"required,min=8"`
    Status   string `json:"status" bson:"status" validate:"oneof=active inactive suspended"`
}
```

### **✅ 扩展字段**
```go
// 个人信息
FirstName string `json:"first_name,omitempty" bson:"first_name,omitempty"`
LastName  string `json:"last_name,omitempty" bson:"last_name,omitempty"`
Phone     string `json:"phone,omitempty" bson:"phone,omitempty"`
Avatar    string `json:"avatar,omitempty" bson:"avatar,omitempty"`

// 角色和权限
Role        string   `json:"role" bson:"role" validate:"oneof=admin user guest"`
Permissions []string `json:"permissions,omitempty" bson:"permissions,omitempty"`

// 登录信息
LastLoginAt    *time.Time `json:"last_login_at,omitempty" bson:"last_login_at,omitempty"`
LastLoginIP    string     `json:"last_login_ip,omitempty" bson:"last_login_ip,omitempty"`
LoginAttempts  int        `json:"login_attempts" bson:"login_attempts"`
LockedUntil    *time.Time `json:"locked_until,omitempty" bson:"locked_until,omitempty"`

// 邮箱验证
EmailVerified   bool       `json:"email_verified" bson:"email_verified"`
EmailVerifiedAt *time.Time `json:"email_verified_at,omitempty" bson:"email_verified_at,omitempty"`
```

### **✅ 安全字段**
```go
// 密码重置（不在JSON中返回）
PasswordResetToken     string     `json:"-" bson:"password_reset_token,omitempty"`
PasswordResetExpiresAt *time.Time `json:"-" bson:"password_reset_expires_at,omitempty"`

// 邮箱验证令牌（不在JSON中返回）
EmailVerificationToken     string     `json:"-" bson:"email_verification_token,omitempty"`
EmailVerificationExpiresAt *time.Time `json:"-" bson:"email_verification_expires_at,omitempty"`
```

## 🎁 **核心方法**

### **✅ 基础方法**
```go
// TableName 返回集合名称
func (u *User) TableName() string

// GetID 获取用户ID
func (u *User) GetID() bson.ObjectID

// GetIDString 获取用户ID字符串
func (u *User) GetIDString() string
```

### **✅ 状态检查方法**
```go
// IsActive 检查用户是否激活
func (u *User) IsActive() bool

// IsLocked 检查用户是否被锁定
func (u *User) IsLocked() bool

// IsEmailVerified 检查邮箱是否已验证
func (u *User) IsEmailVerified() bool

// IsAdmin 检查用户是否是管理员
func (u *User) IsAdmin() bool
```

### **✅ 权限管理方法**
```go
// HasPermission 检查用户是否有指定权限
func (u *User) HasPermission(permission string) bool

// GetFullName 获取用户全名
func (u *User) GetFullName() string
```

### **✅ 安全操作方法**
```go
// ClearSensitiveInfo 清除敏感信息（用于API响应）
func (u *User) ClearSensitiveInfo()

// UpdateLoginInfo 更新登录信息
func (u *User) UpdateLoginInfo(ip string)

// IncrementLoginAttempts 增加登录尝试次数
func (u *User) IncrementLoginAttempts()

// LockAccount 锁定账户
func (u *User) LockAccount(duration time.Duration)
```

### **✅ 验证相关方法**
```go
// VerifyEmail 验证邮箱
func (u *User) VerifyEmail()

// SetPasswordResetToken 设置密码重置令牌
func (u *User) SetPasswordResetToken(token string, expiresAt time.Time)

// SetEmailVerificationToken 设置邮箱验证令牌
func (u *User) SetEmailVerificationToken(token string, expiresAt time.Time)
```

## 🚀 **常量定义**

### **✅ 用户状态常量**
```go
const (
    UserStatusActive    = "active"
    UserStatusInactive  = "inactive"
    UserStatusSuspended = "suspended"
)
```

### **✅ 用户角色常量**
```go
const (
    UserRoleAdmin = "admin"
    UserRoleUser  = "user"
    UserRoleGuest = "guest"
)
```

### **✅ 权限常量**
```go
const (
    PermissionUserRead   = "user:read"
    PermissionUserWrite  = "user:write"
    PermissionUserDelete = "user:delete"
    PermissionAdminPanel = "admin:panel"
)
```

## 🎯 **工厂方法**

### **✅ 创建普通用户**
```go
// NewUser 创建新用户实例
func NewUser(username, email, password string) *User {
    user := &User{
        Username: username,
        Email:    email,
        Password: password,
    }
    user.SetDefaultValues()
    return user
}
```

### **✅ 创建管理员用户**
```go
// NewAdminUser 创建新管理员用户实例
func NewAdminUser(username, email, password string) *User {
    user := NewUser(username, email, password)
    user.Role = UserRoleAdmin
    user.Permissions = []string{
        PermissionUserRead,
        PermissionUserWrite,
        PermissionUserDelete,
        PermissionAdminPanel,
    }
    return user
}
```

## 🔗 **与 UserService 的集成**

### **✅ 完美匹配 CreateUser 方法**
```go
// user_service.go 中的使用
user := &model.User{
    Username:  req.Username,
    Email:     req.Email,
    Password:  hashedPassword,
    Status:    "active",
    CreatedAt: time.Now(),
    UpdatedAt: time.Now(),
}
```

### **✅ 支持所有业务逻辑**
- ✅ 邮箱格式验证
- ✅ 密码强度检查
- ✅ 邮箱唯一性验证
- ✅ 密码加密存储
- ✅ 敏感信息清除
- ✅ 登录状态跟踪
- ✅ 账户锁定机制

## 🎁 **使用示例**

### **创建新用户**
```go
// 使用工厂方法
user := model.NewUser("john_doe", "<EMAIL>", "password123")

// 或者直接创建
user := &model.User{
    Username: "john_doe",
    Email:    "<EMAIL>",
    Password: "hashedPassword",
}
user.SetDefaultValues()
```

### **检查用户状态**
```go
if user.IsActive() && !user.IsLocked() {
    // 用户可以登录
}

if user.IsAdmin() {
    // 管理员权限
}

if user.HasPermission("user:write") {
    // 有写权限
}
```

### **更新登录信息**
```go
user.UpdateLoginInfo("192.168.1.100")
```

### **处理登录失败**
```go
user.IncrementLoginAttempts()
if user.LoginAttempts >= 5 {
    user.LockAccount(30 * time.Minute)
}
```

## 🏆 **总结**

创建的 User 模型具有以下特点：

1. **✅ 完整性** - 涵盖了用户管理的所有必要字段
2. **✅ 安全性** - 支持密码加密、账户锁定、令牌管理
3. **✅ 灵活性** - 支持角色权限、状态管理、扩展字段
4. **✅ 易用性** - 提供丰富的方法和工厂函数
5. **✅ 兼容性** - 完美匹配现有的 UserService 业务逻辑
6. **✅ 可扩展性** - 便于后续添加新功能

这个 User 模型为你的用户管理系统提供了坚实的基础！
