package config

import (
	"fmt"
	"log"
	"os"
	"strconv"

	"github.com/joho/godotenv"
)

type Config struct {
	MongoDB MongoDBConfig
	Redis   RedisConfig
	Server  ServerConfig
}

type MongoDBConfig struct {
	Host     string
	Port     int
	DBName   string
	Username string
	Password string
}

type RedisConfig struct {
	Host     string
	Port     int
	Password string
}

type ServerConfig struct {
	Port string
	Mode string // debug, release, test
}

var AppConfig *Config

func init() {
	AppConfig = &Config{
		MongoDB: MongoDBConfig{
			Host:     getEnv("MONGODB_HOST", "localhost"),
			Port:     getEnvAsInt("MONGODB_PORT", 27017),
			DBName:   getEnv("MONGODB_DB_NAME", "demoDb"),
			Username: getEnv("MONGODB_USERNAME", ""),
			Password: getEnv("MONGODB_PASSWORD", ""),
		},
		Redis: RedisConfig{
			Host:     getEnv("REDIS_HOST", "localhost"),
			Port:     getEnvAsInt("REDIS_PORT", 6379),
			Password: getEnv("REDIS_PASSWORD", ""),
		},
		Server: ServerConfig{
			Port: getEnv("SERVER_PORT", "5018"),
			Mode: getEnv("GIN_MODE", "debug"),
		},
	}
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getEnvAsInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

func (c *MongoDBConfig) GetConnectionString() string {
	if c.Username != "" && c.Password != "" {
		return fmt.Sprintf("mongodb://%s:%s@%s:%d", c.Username, c.Password, c.Host, c.Port)
	}
	return fmt.Sprintf("mongodb://%s:%d", c.Host, c.Port)
}
