# MongoDB数据库设置指南

## 🎯 当前状态

✅ **项目已配置为支持MongoDB数据库**
✅ **当前使用内存数据库进行演示**
✅ **可以轻松切换到真实MongoDB**

## 📋 数据库切换方法

### 方法1：使用Docker（推荐）

#### 1. 安装Docker
- 下载并安装 [Docker Desktop](https://www.docker.com/products/docker-desktop/)

#### 2. 启动MongoDB容器
```bash
# 使用提供的docker-compose.yml
docker-compose up -d

# 或者单独运行MongoDB
docker run -d \
  --name demo-mongodb \
  -p 27017:27017 \
  -e MONGO_INITDB_ROOT_USERNAME=admin \
  -e MONGO_INITDB_ROOT_PASSWORD=password \
  -e MONGO_INITDB_DATABASE=demoDb \
  mongo:7.0
```

#### 3. 更新配置
修改 `.env` 文件：
```env
# Database Configuration
DB_TYPE=mongodb  # 切换到MongoDB
# DB_TYPE=memory  # 内存数据库

# MongoDB Configuration
MONGODB_HOST=localhost
MONGODB_PORT=27017
MONGODB_DB_NAME=demoDb
MONGODB_USERNAME=admin
MONGODB_PASSWORD=password
```

### 方法2：安装本地MongoDB

#### 1. 下载MongoDB
- 访问 [MongoDB官网](https://www.mongodb.com/try/download/community)
- 下载MongoDB Community Server

#### 2. 安装并启动
```bash
# Windows (以管理员身份运行)
net start MongoDB

# 或者手动启动
mongod --dbpath "C:\data\db"
```

#### 3. 更新配置
修改 `.env` 文件：
```env
# Database Configuration
DB_TYPE=mongodb

# MongoDB Configuration (无认证)
MONGODB_HOST=localhost
MONGODB_PORT=27017
MONGODB_DB_NAME=demoDb
MONGODB_USERNAME=
MONGODB_PASSWORD=
```

### 方法3：使用云MongoDB

#### 1. MongoDB Atlas（免费）
- 注册 [MongoDB Atlas](https://www.mongodb.com/cloud/atlas)
- 创建免费集群
- 获取连接字符串

#### 2. 更新配置
修改 `.env` 文件：
```env
DB_TYPE=mongodb

# MongoDB Atlas配置
MONGODB_HOST=your-cluster.mongodb.net
MONGODB_PORT=27017
MONGODB_DB_NAME=demoDb
MONGODB_USERNAME=your-username
MONGODB_PASSWORD=your-password
```

## 🔧 项目架构特点

### 数据库抽象层
项目使用了数据库抽象层，支持：
- ✅ **MongoDB** - 生产环境推荐
- ✅ **内存数据库** - 开发测试使用

### 自动回退机制
```go
// 如果MongoDB连接失败，自动回退到内存数据库
if err := initMongoDB(); err != nil {
    log.Printf("Failed to initialize MongoDB: %v", err)
    log.Println("Falling back to memory database...")
    return initMemoryDB()
}
```

### 配置文件支持
通过 `.env` 文件轻松切换：
```env
DB_TYPE=mongodb  # 使用MongoDB
DB_TYPE=memory   # 使用内存数据库
```

## 🧪 测试验证

### 1. 健康检查
```bash
curl http://localhost:5019/health
```

### 2. 创建任务
```bash
curl -X POST http://localhost:5019/api/v1/tasks \
  -H "Content-Type: application/json" \
  -d '{"name":"测试任务","content":"MongoDB测试内容"}'
```

### 3. 查询任务
```bash
curl http://localhost:5019/api/v1/tasks
```

## 📊 MongoDB特性支持

### ✅ 已实现功能
- **CRUD操作** - 创建、读取、更新、删除
- **分页查询** - 支持page、size参数
- **过滤查询** - 支持字段过滤
- **排序功能** - 支持多字段排序
- **索引优化** - 自动创建常用索引
- **连接池** - 优化数据库连接性能
- **错误处理** - 完善的错误处理机制
- **时间戳** - 自动管理created_at、updated_at

### 🔄 查询操作符
```go
// 等值查询
params["name__eq"] = "任务名称"

// 模糊查询
params["content__like"] = "关键词"

// 排序
params["order"] = "-created_at"  // 降序
params["order"] = "name"         // 升序
```

### 📈 性能优化
- **连接池配置**：最大100个连接，最小10个连接
- **查询超时**：5秒查询超时，10秒连接超时
- **索引策略**：自动为常用字段创建索引

## 🚀 生产环境建议

### 1. 安全配置
```env
# 使用强密码
MONGODB_USERNAME=your-secure-username
MONGODB_PASSWORD=your-strong-password

# 启用SSL（生产环境）
MONGODB_SSL=true
```

### 2. 性能调优
- 根据实际负载调整连接池大小
- 为查询字段创建合适的索引
- 定期监控数据库性能

### 3. 备份策略
- 定期备份数据库
- 测试恢复流程
- 监控磁盘空间

## 🎉 总结

项目已完全支持MongoDB数据库：
- ✅ **灵活切换** - 内存数据库 ↔ MongoDB
- ✅ **自动回退** - MongoDB失败时自动使用内存数据库
- ✅ **完整功能** - 支持所有CRUD操作和高级查询
- ✅ **生产就绪** - 连接池、错误处理、性能优化

只需要：
1. 启动MongoDB服务
2. 修改 `.env` 中的 `DB_TYPE=mongodb`
3. 重启应用程序

就可以使用真实的MongoDB数据库了！
