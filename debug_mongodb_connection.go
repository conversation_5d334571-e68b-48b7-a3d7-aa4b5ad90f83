package main

import (
	"context"
	"demo/config"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/v2/bson"
	"go.mongodb.org/mongo-driver/v2/mongo"
	"go.mongodb.org/mongo-driver/v2/mongo/options"
)

func main() {
	fmt.Println("=== MongoDB连接调试工具 ===")
	fmt.Println("对比数据库管理软件和Go代码的连接方式")
	
	cfg := config.AppConfig.MongoDB
	fmt.Printf("\n配置信息:\n")
	fmt.Printf("  Host: %s\n", cfg.Host)
	fmt.Printf("  Port: %d\n", cfg.Port)
	fmt.Printf("  Database: %s\n", cfg.DBName)
	fmt.Printf("  Username: %s\n", cfg.Username)
	fmt.Printf("  Password: %s\n", maskPassword(cfg.Password))
	
	// 测试不同的连接方式
	testCases := []struct {
		name        string
		uri         string
		description string
		setupFunc   func(*options.ClientOptions)
	}{
		{
			name:        "项目当前方式",
			uri:         cfg.GetConnectionString(),
			description: "项目中使用的连接方式",
			setupFunc: func(opts *options.ClientOptions) {
				opts.SetMaxPoolSize(100)
				opts.SetMinPoolSize(10)
				opts.SetMaxConnIdleTime(30 * time.Second)
				opts.SetConnectTimeout(10 * time.Second)
				opts.SetServerSelectionTimeout(5 * time.Second)
			},
		},
		{
			name:        "简单连接",
			uri:         cfg.GetConnectionString(),
			description: "最简单的连接方式",
			setupFunc: func(opts *options.ClientOptions) {
				opts.SetConnectTimeout(30 * time.Second)
				opts.SetServerSelectionTimeout(30 * time.Second)
			},
		},
		{
			name:        "直接连接模式",
			uri:         cfg.GetConnectionString(),
			description: "直接连接，不使用服务发现",
			setupFunc: func(opts *options.ClientOptions) {
				opts.SetDirect(true)
				opts.SetConnectTimeout(30 * time.Second)
				opts.SetServerSelectionTimeout(30 * time.Second)
			},
		},
		{
			name:        "指定认证数据库",
			uri:         fmt.Sprintf("mongodb://%s:%s@%s:%d/%s?authSource=admin", cfg.Username, cfg.Password, cfg.Host, cfg.Port, cfg.DBName),
			description: "指定认证数据库为admin",
			setupFunc: func(opts *options.ClientOptions) {
				opts.SetConnectTimeout(30 * time.Second)
				opts.SetServerSelectionTimeout(30 * time.Second)
			},
		},
		{
			name:        "禁用SSL",
			uri:         cfg.GetConnectionString() + "?ssl=false",
			description: "明确禁用SSL连接",
			setupFunc: func(opts *options.ClientOptions) {
				opts.SetConnectTimeout(30 * time.Second)
				opts.SetServerSelectionTimeout(30 * time.Second)
			},
		},
		{
			name:        "兼容模式",
			uri:         cfg.GetConnectionString() + "?retryWrites=false&w=majority",
			description: "兼容模式，禁用重试写入",
			setupFunc: func(opts *options.ClientOptions) {
				opts.SetConnectTimeout(30 * time.Second)
				opts.SetServerSelectionTimeout(30 * time.Second)
			},
		},
	}
	
	for i, tc := range testCases {
		fmt.Printf("\n%d. 测试: %s\n", i+1, tc.name)
		fmt.Printf("   描述: %s\n", tc.description)
		fmt.Printf("   URI: %s\n", tc.uri)
		
		success := testConnection(tc.uri, tc.setupFunc)
		if success {
			fmt.Printf("   ✅ 连接成功!\n")
			
			// 如果连接成功，尝试一些基本操作
			if testOperations(tc.uri, tc.setupFunc) {
				fmt.Printf("   ✅ 数据库操作成功!\n")
				fmt.Printf("   🎉 这个连接方式可以使用!\n")
				break // 找到可用的连接方式就停止
			}
		} else {
			fmt.Printf("   ❌ 连接失败\n")
		}
	}
	
	fmt.Println("\n=== 建议 ===")
	fmt.Println("如果某个连接方式成功，请更新项目代码使用该方式")
	fmt.Println("如果所有方式都失败，请检查:")
	fmt.Println("1. 数据库管理软件使用的确切连接参数")
	fmt.Println("2. 是否有代理或VPN影响连接")
	fmt.Println("3. Go程序的网络权限")
}

func testConnection(uri string, setupFunc func(*options.ClientOptions)) bool {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	
	clientOptions := options.Client().ApplyURI(uri)
	setupFunc(clientOptions)
	
	client, err := mongo.Connect(clientOptions)
	if err != nil {
		fmt.Printf("   错误: 连接创建失败 - %v\n", err)
		return false
	}
	defer client.Disconnect(ctx)
	
	if err := client.Ping(ctx, nil); err != nil {
		fmt.Printf("   错误: Ping失败 - %v\n", err)
		return false
	}
	
	return true
}

func testOperations(uri string, setupFunc func(*options.ClientOptions)) bool {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	
	clientOptions := options.Client().ApplyURI(uri)
	setupFunc(clientOptions)
	
	client, err := mongo.Connect(clientOptions)
	if err != nil {
		return false
	}
	defer client.Disconnect(ctx)
	
	// 列出数据库
	databases, err := client.ListDatabaseNames(ctx, bson.D{})
	if err != nil {
		fmt.Printf("   警告: 列出数据库失败 - %v\n", err)
		return false
	}
	
	fmt.Printf("   可用数据库: %v\n", databases)
	
	// 测试指定数据库的操作
	cfg := config.AppConfig.MongoDB
	db := client.Database(cfg.DBName)
	
	// 列出集合
	collections, err := db.ListCollectionNames(ctx, bson.D{})
	if err != nil {
		fmt.Printf("   警告: 列出集合失败 - %v\n", err)
		return false
	}
	
	fmt.Printf("   数据库 %s 中的集合: %v\n", cfg.DBName, collections)
	
	return true
}

func maskPassword(password string) string {
	if password == "" {
		return "(空)"
	}
	if len(password) <= 2 {
		return "***"
	}
	return password[:2] + "***"
}
