package validator

import (
	"fmt"
	"regexp"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
)

var validate *validator.Validate

func init() {
	validate = validator.New()
	
	// 注册自定义验证器
	validate.RegisterValidation("objectid", validateObjectID)
	validate.RegisterValidation("phone", validatePhone)
	validate.RegisterValidation("chinese", validateChinese)
}

// Validate 验证结构体
func Validate(s interface{}) error {
	return validate.Struct(s)
}

// ValidateVar 验证单个变量
func ValidateVar(field interface{}, tag string) error {
	return validate.Var(field, tag)
}

// GetValidationErrors 获取格式化的验证错误
func GetValidationErrors(err error) map[string]string {
	errors := make(map[string]string)
	
	if validationErrors, ok := err.(validator.ValidationErrors); ok {
		for _, e := range validationErrors {
			errors[strings.ToLower(e.<PERSON>())] = getErrorMessage(e)
		}
	}
	
	return errors
}

// getErrorMessage 获取错误消息
func getErrorMessage(e validator.FieldError) string {
	switch e.Tag() {
	case "required":
		return fmt.Sprintf("%s is required", e.Field())
	case "email":
		return fmt.Sprintf("%s must be a valid email", e.Field())
	case "min":
		return fmt.Sprintf("%s must be at least %s characters", e.Field(), e.Param())
	case "max":
		return fmt.Sprintf("%s must be at most %s characters", e.Field(), e.Param())
	case "len":
		return fmt.Sprintf("%s must be exactly %s characters", e.Field(), e.Param())
	case "objectid":
		return fmt.Sprintf("%s must be a valid ObjectID", e.Field())
	case "phone":
		return fmt.Sprintf("%s must be a valid phone number", e.Field())
	case "chinese":
		return fmt.Sprintf("%s must contain only Chinese characters", e.Field())
	default:
		return fmt.Sprintf("%s is invalid", e.Field())
	}
}

// 自定义验证器

// validateObjectID 验证ObjectID格式
func validateObjectID(fl validator.FieldLevel) bool {
	objectIDRegex := regexp.MustCompile("^[a-fA-F0-9]{24}$")
	return objectIDRegex.MatchString(fl.Field().String())
}

// validatePhone 验证手机号格式
func validatePhone(fl validator.FieldLevel) bool {
	phoneRegex := regexp.MustCompile(`^1[3-9]\d{9}$`)
	return phoneRegex.MatchString(fl.Field().String())
}

// validateChinese 验证中文字符
func validateChinese(fl validator.FieldLevel) bool {
	chineseRegex := regexp.MustCompile(`^[\p{Han}]+$`)
	return chineseRegex.MatchString(fl.Field().String())
}

// BindAndValidate 绑定并验证请求
func BindAndValidate(c *gin.Context, obj interface{}) error {
	// 绑定请求数据
	if err := c.ShouldBindJSON(obj); err != nil {
		return fmt.Errorf("bind error: %v", err)
	}
	
	// 验证数据
	if err := Validate(obj); err != nil {
		return err
	}
	
	return nil
}

// ValidationErrorResponse 验证错误响应
type ValidationErrorResponse struct {
	Message string            `json:"message"`
	Errors  map[string]string `json:"errors"`
}

// NewValidationErrorResponse 创建验证错误响应
func NewValidationErrorResponse(err error) ValidationErrorResponse {
	return ValidationErrorResponse{
		Message: "Validation failed",
		Errors:  GetValidationErrors(err),
	}
}
