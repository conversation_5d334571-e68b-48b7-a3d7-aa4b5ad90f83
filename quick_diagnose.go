package main

import (
	"context"
	"demo/config"
	"fmt"
	"net"
	"time"

	"go.mongodb.org/mongo-driver/v2/mongo"
	"go.mongodb.org/mongo-driver/v2/mongo/options"
)

func main() {
	fmt.Println("快速MongoDB诊断...")
	
	cfg := config.AppConfig.MongoDB
	fmt.Printf("目标: %s:%d\n", cfg.Host, cfg.Port)
	
	// 1. 快速网络测试
	fmt.Println("\n1. 网络连接测试...")
	address := fmt.Sprintf("%s:%d", cfg.Host, cfg.Port)
	
	conn, err := net.DialTimeout("tcp", address, 2*time.Second)
	if err != nil {
		fmt.Printf("❌ 网络连接失败: %v\n", err)
		
		// 测试本地MongoDB
		fmt.Println("\n尝试连接本地MongoDB...")
		testLocalMongoDB()
		return
	}
	conn.Close()
	fmt.Println("✅ 网络连接成功")
	
	// 2. MongoDB连接测试
	fmt.Println("\n2. MongoDB连接测试...")
	testMongoDB(cfg)
	
	// 3. 建议
	fmt.Println("\n3. 建议解决方案...")
	suggestSolutions(cfg)
}

func testLocalMongoDB() {
	fmt.Println("测试本地MongoDB (localhost:27017)...")
	
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()
	
	clientOptions := options.Client().ApplyURI("mongodb://localhost:27017")
	clientOptions.SetConnectTimeout(2 * time.Second)
	clientOptions.SetServerSelectionTimeout(2 * time.Second)
	
	client, err := mongo.Connect(clientOptions)
	if err != nil {
		fmt.Printf("❌ 本地MongoDB连接失败: %v\n", err)
		return
	}
	defer client.Disconnect(ctx)
	
	if err := client.Ping(ctx, nil); err != nil {
		fmt.Printf("❌ 本地MongoDB Ping失败: %v\n", err)
		return
	}
	
	fmt.Println("✅ 本地MongoDB连接成功!")
	fmt.Println("建议: 可以暂时使用本地MongoDB进行开发")
}

func testMongoDB(cfg config.MongoDBConfig) {
	// 测试无认证连接
	fmt.Println("测试无认证连接...")
	uri := fmt.Sprintf("mongodb://%s:%d", cfg.Host, cfg.Port)
	if testConnection(uri, 3*time.Second) {
		fmt.Println("✅ 无认证连接成功")
		
		// 测试有认证连接
		fmt.Println("测试有认证连接...")
		authURI := cfg.GetConnectionString()
		if testConnection(authURI, 3*time.Second) {
			fmt.Println("✅ 有认证连接成功")
		} else {
			fmt.Println("❌ 认证失败 - 检查用户名密码")
		}
	} else {
		fmt.Println("❌ 无认证连接失败")
	}
}

func testConnection(uri string, timeout time.Duration) bool {
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()
	
	clientOptions := options.Client().ApplyURI(uri)
	clientOptions.SetConnectTimeout(timeout)
	clientOptions.SetServerSelectionTimeout(timeout)
	
	client, err := mongo.Connect(clientOptions)
	if err != nil {
		return false
	}
	defer client.Disconnect(ctx)
	
	return client.Ping(ctx, nil) == nil
}

func suggestSolutions(cfg config.MongoDBConfig) {
	fmt.Println("可能的解决方案:")
	fmt.Println("1. 检查MongoDB服务状态:")
	fmt.Printf("   ssh %s 'sudo systemctl status mongod'\n", cfg.Host)
	
	fmt.Println("2. 检查MongoDB配置文件 (/etc/mongod.conf):")
	fmt.Println("   net:")
	fmt.Printf("     port: %d\n", cfg.Port)
	fmt.Println("     bindIp: 0.0.0.0  # 允许外部连接")
	
	fmt.Println("3. 检查防火墙:")
	fmt.Printf("   sudo ufw allow %d\n", cfg.Port)
	
	fmt.Println("4. 临时解决方案 - 使用本地MongoDB:")
	fmt.Println("   修改.env文件:")
	fmt.Println("   MONGODB_HOST=localhost")
	fmt.Println("   MONGODB_PORT=27017")
	fmt.Println("   MONGODB_USERNAME=")
	fmt.Println("   MONGODB_PASSWORD=")
	
	fmt.Println("5. 或者继续使用内存数据库:")
	fmt.Println("   DB_TYPE=memory")
}
