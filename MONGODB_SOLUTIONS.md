# MongoDB连接问题解决方案

## 🔍 问题分析

根据诊断结果，发现以下问题：

### 远程MongoDB (***************:37017)
- ❌ 网络连接超时
- ❌ 无法建立TCP连接
- 可能原因：服务器未运行、网络问题、防火墙

### 本地MongoDB (localhost:27017)
- ❌ 连接被拒绝
- ❌ 端口27017未监听
- ❌ 没有mongo相关进程运行

## 🚀 解决方案

### 方案1: 启动本地MongoDB服务

#### Windows系统：

1. **检查MongoDB是否已安装**
   ```cmd
   mongod --version
   ```

2. **如果已安装，启动MongoDB服务**
   ```cmd
   # 方法1: 使用服务管理器
   net start MongoDB
   
   # 方法2: 手动启动
   mongod --dbpath "C:\data\db"
   ```

3. **如果未安装，下载安装MongoDB**
   - 访问: https://www.mongodb.com/try/download/community
   - 下载Windows版本
   - 安装后启动服务

#### 验证本地MongoDB
```bash
# 测试连接
mongo --eval "db.runCommand('ping')"
```

### 方案2: 使用Docker运行MongoDB

```bash
# 拉取MongoDB镜像
docker pull mongo:latest

# 运行MongoDB容器
docker run -d \
  --name mongodb \
  -p 27017:27017 \
  -e MONGO_INITDB_ROOT_USERNAME=admin \
  -e MONGO_INITDB_ROOT_PASSWORD=password \
  mongo:latest

# 验证运行状态
docker ps | grep mongodb
```

### 方案3: 修改项目配置使用本地MongoDB

修改`.env`文件：
```env
# 使用本地MongoDB
DB_TYPE=mongodb
MONGODB_HOST=localhost
MONGODB_PORT=27017
MONGODB_DB_NAME=demoDb
MONGODB_USERNAME=
MONGODB_PASSWORD=

# 如果使用Docker MongoDB with auth
# MONGODB_USERNAME=admin
# MONGODB_PASSWORD=password
```

### 方案4: 修复远程MongoDB连接

#### 检查远程服务器
```bash
# SSH到远程服务器
ssh user@***************

# 检查MongoDB服务状态
sudo systemctl status mongod

# 启动MongoDB服务
sudo systemctl start mongod

# 检查端口监听
sudo netstat -tlnp | grep 37017
```

#### 检查MongoDB配置
编辑 `/etc/mongod.conf`:
```yaml
net:
  port: 37017
  bindIp: 0.0.0.0  # 允许外部连接

security:
  authorization: enabled
```

#### 检查防火墙
```bash
# Ubuntu/Debian
sudo ufw allow 37017

# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=37017/tcp
sudo firewall-cmd --reload
```

### 方案5: 临时使用内存数据库

如果暂时无法解决MongoDB问题，可以继续使用内存数据库：

修改`.env`文件：
```env
# 临时使用内存数据库
DB_TYPE=memory
```

## 🔧 快速测试脚本

我已经为您创建了测试脚本，可以快速验证解决方案：

### 测试本地MongoDB
```bash
go run test_local_mongodb.go
```

### 测试远程MongoDB
```bash
go run quick_diagnose.go
```

## 📋 推荐步骤

### 立即可行的解决方案：

1. **最快解决方案** - 使用Docker MongoDB：
   ```bash
   docker run -d --name mongodb -p 27017:27017 mongo:latest
   ```
   然后修改`.env`:
   ```env
   MONGODB_HOST=localhost
   MONGODB_PORT=27017
   MONGODB_USERNAME=
   MONGODB_PASSWORD=
   ```

2. **如果有本地MongoDB** - 启动服务：
   ```cmd
   net start MongoDB
   ```

3. **如果都不行** - 继续使用内存数据库：
   ```env
   DB_TYPE=memory
   ```

## 🎯 验证步骤

完成任一解决方案后：

1. **重启应用**
   ```bash
   # 停止当前服务器 (Ctrl+C)
   # 重新启动
   go run main.go
   ```

2. **检查日志**
   - 应该看到 "MongoDB database initialized successfully"
   - 而不是 "Falling back to memory database"

3. **测试API**
   ```bash
   curl http://localhost:9090/health
   ```

## 🔮 长期建议

1. **开发环境**: 使用Docker MongoDB，便于管理
2. **生产环境**: 使用专用MongoDB服务器或云服务
3. **备份方案**: 保持内存数据库作为fallback机制

选择最适合您当前环境的解决方案，我可以帮助您实施！
