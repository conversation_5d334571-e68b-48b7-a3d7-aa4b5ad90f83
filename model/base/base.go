package base

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type ModelBase struct {
	ID        primitive.ObjectID `json:"id,omitempty" bson:"_id,omitempty" mongox:"autoID"`
	CreatedAt time.Time          `json:"created_at,omitempty" bson:"created_at"`
	UpdatedAt time.Time          `json:"updated_at,omitempty" bson:"updated_at"`
	DeletedAt time.Time          `json:"deleted_at,omitempty" bson:"deleted_at,omitempty"`
}

func (m *ModelBase) DefaultId() primitive.ObjectID {
	if m.ID.IsZero() {
		m.ID = primitive.NewObjectID()
	}
	return m.ID
}

func (m *ModelBase) DefaultCreatedAt() time.Time {
	if m.CreatedAt.IsZero() {
		m.CreatedAt = time.Now().Local()
	}
	return m.CreatedAt
}

func (m *ModelBase) DefaultUpdatedAt() time.Time {
	m.UpdatedAt = time.Now().Local()
	return m.UpdatedAt
}
