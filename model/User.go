package model

import (
	"demo/model/base"
	"time"

	"go.mongodb.org/mongo-driver/v2/bson"
)

// User 用户模型
type User struct {
	base.ModelBase `bson:",inline"`
	
	// 基本信息
	Username string `json:"username" bson:"username" validate:"required,min=3,max=50"`
	Email    string `json:"email" bson:"email" validate:"required,email"`
	Password string `json:"password,omitempty" bson:"password" validate:"required,min=8"`
	
	// 用户状态
	Status string `json:"status" bson:"status" validate:"oneof=active inactive suspended"`
	
	// 个人信息
	FirstName string `json:"first_name,omitempty" bson:"first_name,omitempty" validate:"max=50"`
	LastName  string `json:"last_name,omitempty" bson:"last_name,omitempty" validate:"max=50"`
	Phone     string `json:"phone,omitempty" bson:"phone,omitempty" validate:"max=20"`
	Avatar    string `json:"avatar,omitempty" bson:"avatar,omitempty"`
	
	// 角色和权限
	Role        string   `json:"role" bson:"role" validate:"oneof=admin user guest"`
	Permissions []string `json:"permissions,omitempty" bson:"permissions,omitempty"`
	
	// 登录信息
	LastLoginAt    *time.Time `json:"last_login_at,omitempty" bson:"last_login_at,omitempty"`
	LastLoginIP    string     `json:"last_login_ip,omitempty" bson:"last_login_ip,omitempty"`
	LoginAttempts  int        `json:"login_attempts" bson:"login_attempts"`
	LockedUntil    *time.Time `json:"locked_until,omitempty" bson:"locked_until,omitempty"`
	
	// 邮箱验证
	EmailVerified   bool       `json:"email_verified" bson:"email_verified"`
	EmailVerifiedAt *time.Time `json:"email_verified_at,omitempty" bson:"email_verified_at,omitempty"`
	
	// 密码重置
	PasswordResetToken     string     `json:"-" bson:"password_reset_token,omitempty"`
	PasswordResetExpiresAt *time.Time `json:"-" bson:"password_reset_expires_at,omitempty"`
	
	// 邮箱验证令牌
	EmailVerificationToken     string     `json:"-" bson:"email_verification_token,omitempty"`
	EmailVerificationExpiresAt *time.Time `json:"-" bson:"email_verification_expires_at,omitempty"`
}

// TableName 返回集合名称
func (u *User) TableName() string {
	return "users"
}

// GetID 获取用户ID
func (u *User) GetID() bson.ObjectID {
	return u.ID
}

// GetIDString 获取用户ID字符串
func (u *User) GetIDString() string {
	return u.ID.Hex()
}

// IsActive 检查用户是否激活
func (u *User) IsActive() bool {
	return u.Status == "active"
}

// IsLocked 检查用户是否被锁定
func (u *User) IsLocked() bool {
	if u.LockedUntil == nil {
		return false
	}
	return time.Now().Before(*u.LockedUntil)
}

// IsEmailVerified 检查邮箱是否已验证
func (u *User) IsEmailVerified() bool {
	return u.EmailVerified
}

// GetFullName 获取用户全名
func (u *User) GetFullName() string {
	if u.FirstName == "" && u.LastName == "" {
		return u.Username
	}
	if u.FirstName == "" {
		return u.LastName
	}
	if u.LastName == "" {
		return u.FirstName
	}
	return u.FirstName + " " + u.LastName
}

// HasPermission 检查用户是否有指定权限
func (u *User) HasPermission(permission string) bool {
	for _, p := range u.Permissions {
		if p == permission {
			return true
		}
	}
	return false
}

// IsAdmin 检查用户是否是管理员
func (u *User) IsAdmin() bool {
	return u.Role == "admin"
}

// ClearSensitiveInfo 清除敏感信息（用于API响应）
func (u *User) ClearSensitiveInfo() {
	u.Password = ""
	u.PasswordResetToken = ""
	u.EmailVerificationToken = ""
}

// SetDefaultValues 设置默认值
func (u *User) SetDefaultValues() {
	if u.Status == "" {
		u.Status = "active"
	}
	if u.Role == "" {
		u.Role = "user"
	}
	if u.Permissions == nil {
		u.Permissions = []string{}
	}
	u.EmailVerified = false
	u.LoginAttempts = 0
	
	// 设置基础模型的默认值
	u.DefaultId()
	u.DefaultCreatedAt()
	u.DefaultUpdatedAt()
}

// UpdateLoginInfo 更新登录信息
func (u *User) UpdateLoginInfo(ip string) {
	now := time.Now()
	u.LastLoginAt = &now
	u.LastLoginIP = ip
	u.LoginAttempts = 0 // 重置登录尝试次数
	u.LockedUntil = nil // 清除锁定状态
	u.DefaultUpdatedAt()
}

// IncrementLoginAttempts 增加登录尝试次数
func (u *User) IncrementLoginAttempts() {
	u.LoginAttempts++
	u.DefaultUpdatedAt()
}

// LockAccount 锁定账户
func (u *User) LockAccount(duration time.Duration) {
	lockUntil := time.Now().Add(duration)
	u.LockedUntil = &lockUntil
	u.DefaultUpdatedAt()
}

// VerifyEmail 验证邮箱
func (u *User) VerifyEmail() {
	u.EmailVerified = true
	now := time.Now()
	u.EmailVerifiedAt = &now
	u.EmailVerificationToken = ""
	u.EmailVerificationExpiresAt = nil
	u.DefaultUpdatedAt()
}

// SetPasswordResetToken 设置密码重置令牌
func (u *User) SetPasswordResetToken(token string, expiresAt time.Time) {
	u.PasswordResetToken = token
	u.PasswordResetExpiresAt = &expiresAt
	u.DefaultUpdatedAt()
}

// ClearPasswordResetToken 清除密码重置令牌
func (u *User) ClearPasswordResetToken() {
	u.PasswordResetToken = ""
	u.PasswordResetExpiresAt = nil
	u.DefaultUpdatedAt()
}

// SetEmailVerificationToken 设置邮箱验证令牌
func (u *User) SetEmailVerificationToken(token string, expiresAt time.Time) {
	u.EmailVerificationToken = token
	u.EmailVerificationExpiresAt = &expiresAt
	u.DefaultUpdatedAt()
}

// ClearEmailVerificationToken 清除邮箱验证令牌
func (u *User) ClearEmailVerificationToken() {
	u.EmailVerificationToken = ""
	u.EmailVerificationExpiresAt = nil
	u.DefaultUpdatedAt()
}

// UserStatus 用户状态常量
const (
	UserStatusActive    = "active"
	UserStatusInactive  = "inactive"
	UserStatusSuspended = "suspended"
)

// UserRole 用户角色常量
const (
	UserRoleAdmin = "admin"
	UserRoleUser  = "user"
	UserRoleGuest = "guest"
)

// UserPermissions 用户权限常量
const (
	PermissionUserRead   = "user:read"
	PermissionUserWrite  = "user:write"
	PermissionUserDelete = "user:delete"
	PermissionAdminPanel = "admin:panel"
)

// NewUser 创建新用户实例
func NewUser(username, email, password string) *User {
	user := &User{
		Username: username,
		Email:    email,
		Password: password,
	}
	user.SetDefaultValues()
	return user
}

// NewAdminUser 创建新管理员用户实例
func NewAdminUser(username, email, password string) *User {
	user := NewUser(username, email, password)
	user.Role = UserRoleAdmin
	user.Permissions = []string{
		PermissionUserRead,
		PermissionUserWrite,
		PermissionUserDelete,
		PermissionAdminPanel,
	}
	return user
}
