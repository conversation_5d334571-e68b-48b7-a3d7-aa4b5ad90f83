package main

import (
	"context"
	"demo/config"
	"fmt"
	"log"
	"time"

	"go.mongodb.org/mongo-driver/v2/mongo"
	"go.mongodb.org/mongo-driver/v2/mongo/options"
)

func main() {
	fmt.Println("Testing MongoDB connection...")
	
	// 加载配置
	cfg := config.AppConfig
	
	fmt.Printf("MongoDB Configuration:\n")
	fmt.Printf("  Host: %s\n", cfg.MongoDB.Host)
	fmt.Printf("  Port: %d\n", cfg.MongoDB.Port)
	fmt.Printf("  Database: %s\n", cfg.MongoDB.DBName)
	fmt.Printf("  Username: %s\n", cfg.MongoDB.Username)
	fmt.Printf("  Password: %s\n", maskPassword(cfg.MongoDB.Password))
	fmt.Printf("  Connection String: %s\n", cfg.MongoDB.GetConnectionString())
	
	// 创建连接
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	
	clientOptions := options.Client().ApplyURI(cfg.MongoDB.GetConnectionString())
	clientOptions.SetMaxPoolSize(10)
	clientOptions.SetMinPoolSize(1)
	clientOptions.SetMaxConnIdleTime(30 * time.Second)
	
	fmt.Println("\nAttempting to connect...")
	client, err := mongo.Connect(clientOptions)
	if err != nil {
		log.Fatalf("Failed to create MongoDB client: %v", err)
	}
	defer client.Disconnect(ctx)
	
	fmt.Println("Client created successfully")
	
	// 测试连接
	fmt.Println("Testing ping...")
	pingCtx, pingCancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer pingCancel()
	
	err = client.Ping(pingCtx, nil)
	if err != nil {
		log.Fatalf("Failed to ping MongoDB: %v", err)
	}
	
	fmt.Println("✅ MongoDB connection successful!")
	
	// 测试数据库访问
	db := client.Database(cfg.MongoDB.DBName)
	
	// 列出集合
	fmt.Println("\nListing collections...")
	collections, err := db.ListCollectionNames(ctx, map[string]interface{}{})
	if err != nil {
		log.Printf("Failed to list collections: %v", err)
	} else {
		fmt.Printf("Found %d collections: %v\n", len(collections), collections)
	}
	
	// 测试插入一个文档
	fmt.Println("\nTesting document insertion...")
	collection := db.Collection("test_connection")
	
	testDoc := map[string]interface{}{
		"test":      true,
		"timestamp": time.Now(),
		"message":   "Connection test successful",
	}
	
	result, err := collection.InsertOne(ctx, testDoc)
	if err != nil {
		log.Printf("Failed to insert test document: %v", err)
	} else {
		fmt.Printf("✅ Test document inserted with ID: %v\n", result.InsertedID)
	}
	
	// 清理测试文档
	_, err = collection.DeleteOne(ctx, map[string]interface{}{"test": true})
	if err != nil {
		log.Printf("Failed to delete test document: %v", err)
	} else {
		fmt.Println("✅ Test document cleaned up")
	}
	
	fmt.Println("\n🎉 All MongoDB tests passed!")
}

func maskPassword(password string) string {
	if password == "" {
		return "(empty)"
	}
	if len(password) <= 2 {
		return "***"
	}
	return password[:2] + "***"
}
