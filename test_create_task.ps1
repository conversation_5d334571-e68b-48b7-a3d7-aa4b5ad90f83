try {
    Write-Host "Testing create task..." -ForegroundColor Yellow
    
    $taskData = @{
        name = "MongoDB v2 Test Task"
        content = "Testing with new database abstraction layer"
    } | ConvertTo-Json
    
    $response = Invoke-RestMethod -Uri "http://localhost:5019/api/v1/tasks" -Method POST -Body $taskData -ContentType "application/json"
    Write-Host "Task created successfully!" -ForegroundColor Green
    $response | ConvertTo-Json -Depth 3
    
    $taskId = $response.data.id
    Write-Host "`nTask ID: $taskId" -ForegroundColor Cyan
    
    # Test get task
    Write-Host "`nTesting get task..." -ForegroundColor Yellow
    $getResponse = Invoke-RestMethod -Uri "http://localhost:5019/api/v1/tasks/$taskId" -Method GET
    Write-Host "Task retrieved successfully!" -ForegroundColor Green
    $getResponse | ConvertTo-Json -Depth 3
    
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}
