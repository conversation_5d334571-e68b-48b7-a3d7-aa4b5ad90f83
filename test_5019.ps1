# Test port 5019 directly

Write-Host "Testing port 5019 (from .env file)..." -ForegroundColor Yellow

try {
    # Wait a moment for server to be ready
    Start-Sleep -Seconds 2
    
    $response = Invoke-WebRequest -Uri "http://127.0.0.1:5019/health" -Method GET -TimeoutSec 10
    Write-Host "SUCCESS: Health check on port 5019: $($response.StatusCode)" -ForegroundColor Green
    Write-Host $response.Content
    Write-Host ""
    Write-Host ".env file configuration is working correctly!" -ForegroundColor Green
    Write-Host "Server is running on port 5019 as configured in .env file" -ForegroundColor Green
} catch {
    Write-Host "Failed to connect to port 5019: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Error details: $($_.Exception)" -ForegroundColor Red
}
