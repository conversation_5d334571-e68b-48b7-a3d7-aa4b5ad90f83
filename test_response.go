package main

import (
	"demo/response"
	"encoding/json"
	"fmt"
)

func main() {
	// 测试基本响应
	fmt.Println("=== 测试基本响应 ===")
	
	// 成功响应
	successResp := response.Success("操作成功", map[string]string{"name": "test"})
	successJSON, _ := json.MarshalIndent(successResp, "", "  ")
	fmt.Printf("Success Response:\n%s\n\n", successJSON)
	
	// 失败响应
	failResp := response.Fail("参数错误")
	failJSON, _ := json.MarshalIndent(failResp, "", "  ")
	fmt.Printf("Fail Response:\n%s\n\n", failJSON)
	
	// 404响应
	notFoundResp := response.NotFound("资源不存在")
	notFoundJSON, _ := json.MarshalIndent(notFoundResp, "", "  ")
	fmt.Printf("NotFound Response:\n%s\n\n", notFoundJSON)
	
	// 测试分页响应
	fmt.Println("=== 测试分页响应 ===")
	
	data := []map[string]string{
		{"id": "1", "name": "item1"},
		{"id": "2", "name": "item2"},
	}
	
	pageResp := response.SuccessPage(data, 1, 10, 25)
	pageJSON, _ := json.MarshalIndent(pageResp, "", "  ")
	fmt.Printf("Page Response:\n%s\n\n", pageJSON)
	
	// 测试响应构建器
	fmt.Println("=== 测试响应构建器 ===")
	
	builderResp := response.NewResponseBuilder().
		SuccessWithMessage(map[string]string{"key": "value"}, "自定义成功消息").
		WithRequestID("req-123").
		Build()
	
	builderJSON, _ := json.MarshalIndent(builderResp, "", "  ")
	fmt.Printf("Builder Response:\n%s\n\n", builderJSON)
	
	// 测试HTTP状态码
	fmt.Println("=== 测试HTTP状态码 ===")
	fmt.Printf("Success HTTP Status: %d\n", successResp.GetHTTPStatus())
	fmt.Printf("Fail HTTP Status: %d\n", failResp.GetHTTPStatus())
	fmt.Printf("NotFound HTTP Status: %d\n", notFoundResp.GetHTTPStatus())
	fmt.Printf("Page HTTP Status: %d\n", pageResp.GetHTTPStatus())
}
