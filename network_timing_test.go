package main

import (
	"context"
	"demo/config"
	"fmt"
	"net"
	"time"

	"go.mongodb.org/mongo-driver/v2/mongo"
	"go.mongodb.org/mongo-driver/v2/mongo/options"
)

func main() {
	fmt.Println("=== 网络时间测试 ===")
	
	cfg := config.AppConfig.MongoDB
	address := fmt.Sprintf("%s:%d", cfg.Host, cfg.Port)
	
	// 测试多次TCP连接
	fmt.Printf("测试TCP连接到 %s\n", address)
	for i := 1; i <= 5; i++ {
		fmt.Printf("第%d次测试: ", i)
		start := time.Now()
		
		conn, err := net.DialTimeout("tcp", address, 10*time.Second)
		if err != nil {
			fmt.Printf("❌ 失败 (%v) - %v\n", time.Since(start), err)
		} else {
			fmt.Printf("✅ 成功 (%v)\n", time.Since(start))
			conn.Close()
		}
		
		time.Sleep(1 * time.Second)
	}
	
	// 测试MongoDB连接时间
	fmt.Printf("\n测试MongoDB连接到 %s\n", cfg.GetConnectionString())
	for i := 1; i <= 3; i++ {
		fmt.Printf("第%d次MongoDB测试: ", i)
		start := time.Now()
		
		success := testMongoConnection(cfg.GetConnectionString(), 15*time.Second)
		if success {
			fmt.Printf("✅ 成功 (%v)\n", time.Since(start))
		} else {
			fmt.Printf("❌ 失败 (%v)\n", time.Since(start))
		}
		
		time.Sleep(2 * time.Second)
	}
	
	fmt.Println("\n=== 分析 ===")
	fmt.Println("如果TCP连接成功但MongoDB连接失败，可能的原因:")
	fmt.Println("1. MongoDB服务配置问题")
	fmt.Println("2. 认证问题")
	fmt.Println("3. MongoDB版本兼容性")
	fmt.Println("4. 网络延迟导致超时")
	
	fmt.Println("\n建议:")
	fmt.Println("1. 检查数据库管理软件的连接日志")
	fmt.Println("2. 尝试使用mongo shell连接: mongo mongodb://xroot:xrootpwd!@192.168.100.128:37017")
	fmt.Println("3. 检查MongoDB服务器日志")
}

func testMongoConnection(uri string, timeout time.Duration) bool {
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()
	
	clientOptions := options.Client().ApplyURI(uri)
	clientOptions.SetConnectTimeout(timeout)
	clientOptions.SetServerSelectionTimeout(timeout)
	
	client, err := mongo.Connect(clientOptions)
	if err != nil {
		return false
	}
	defer client.Disconnect(ctx)
	
	return client.Ping(ctx, nil) == nil
}
