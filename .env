# Database Configuration
# 切换数据库类型：mongodb 或 memory
# 如果MongoDB不可用，系统会自动回退到内存数据库
DB_TYPE=mongodb  # 使用MongoDB数据库
# DB_TYPE=memory  # 使用内存数据库进行测试

# MongoDB Configuration (当DB_TYPE=mongodb时使用)
MONGODB_HOST=***************
MONGODB_PORT=37017
MONGODB_DB_NAME=demoDb
MONGODB_USERNAME=xroot
MONGODB_PASSWORD=xrootpwd!

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# Server Configuration
SERVER_PORT=9090
GIN_MODE=debug
