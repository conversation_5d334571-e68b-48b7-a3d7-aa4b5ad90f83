try {
    Write-Host "Testing direct connection to localhost:8080..." -ForegroundColor Yellow
    
    # 禁用代理
    $webClient = New-Object System.Net.WebClient
    $webClient.Proxy = $null
    
    # 测试健康检查
    $response = $webClient.DownloadString("http://localhost:8080/health")
    Write-Host "Health check response:" -ForegroundColor Green
    Write-Host $response
    
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    
    # 尝试使用curl
    Write-Host "`nTrying with curl..." -ForegroundColor Yellow
    try {
        $curlResult = & curl.exe -s "http://localhost:8080/health"
        Write-Host "Curl response:" -ForegroundColor Green
        Write-Host $curlResult
    } catch {
        Write-Host "Curl also failed: $($_.Exception.Message)" -ForegroundColor Red
    }
}
