package main

import (
	"demo/config"
	"fmt"
	"net"
	"time"
)

func main() {
	cfg := config.AppConfig
	
	fmt.Println("Network connectivity test for MongoDB")
	fmt.Printf("Target: %s:%d\n", cfg.MongoDB.Host, cfg.MongoDB.Port)
	
	// 测试TCP连接
	fmt.Println("\nTesting TCP connection...")
	address := fmt.Sprintf("%s:%d", cfg.MongoDB.Host, cfg.MongoDB.Port)
	
	conn, err := net.DialTimeout("tcp", address, 10*time.Second)
	if err != nil {
		fmt.Printf("❌ TCP connection failed: %v\n", err)
		
		// 尝试ping主机
		fmt.Println("\nTesting host reachability...")
		testPing(cfg.MongoDB.Host)
		
		return
	}
	defer conn.Close()
	
	fmt.Println("✅ TCP connection successful!")
	fmt.Printf("Local address: %s\n", conn.LocalAddr())
	fmt.Printf("Remote address: %s\n", conn.RemoteAddr())
	
	fmt.Println("\n🎉 Network connectivity test passed!")
	fmt.Println("The MongoDB server appears to be reachable.")
	fmt.Println("If MongoDB connection still fails, check:")
	fmt.Println("1. MongoDB service is running")
	fmt.Println("2. Authentication credentials")
	fmt.Println("3. Database permissions")
}

func testPing(host string) {
	fmt.Printf("Attempting to resolve hostname: %s\n", host)
	
	ips, err := net.LookupIP(host)
	if err != nil {
		fmt.Printf("❌ DNS resolution failed: %v\n", err)
		return
	}
	
	fmt.Printf("✅ DNS resolution successful:\n")
	for _, ip := range ips {
		fmt.Printf("  - %s\n", ip)
	}
}
