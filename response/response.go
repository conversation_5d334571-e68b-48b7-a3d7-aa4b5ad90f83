package response

type Response struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

type ErrorType struct {
	Code    int
	Message string
}

func NewResponse(code int, message string, data interface{}) Response {
	return Response{
		Code:    code,
		Message: message,
		Data:    data,
	}
}
func Success(data interface{}, message string) Response {
	return Response{
		Code:    200,
		Message: message,
		Data:    data,
	}
}
func Fail(message string) Response {
	return Response{
		Code:    201,
		Message: message,
		Data:    nil,
	}
}

type PageResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"message,omitempty"`
	Data    interface{} `json:"data,omitempty"`
	Page    int         `json:"page"`
	Size    int         `json:"size"`
	Total   int64       `json:"total"`
}

func SuccessPage(data interface{}, page int, size int, total int64) PageResponse {
	return PageResponse{
		Code:    200,
		Message: "",
		Data:    data,
		Page:    page,
		Size:    size,
		Total:   total,
	}
}

// 定义错误类型
var (
	ErrorNotFound = ErrorType{Code: 404, Message: "Resource Not Found"}
	ErrorInternal = ErrorType{Code: 500, Message: "Internal Server Error"}
)

func NewErrResponse(errorType ErrorType) Response {
	return NewResponse(errorType.Code, errorType.Message, nil)
}
