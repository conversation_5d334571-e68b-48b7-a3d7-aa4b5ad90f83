package response

import (
	"net/http"
	"time"
)

// ==================== 响应码枚举 ====================

// ResponseCode 响应码枚举
type ResponseCode int

const (
	CodeSuccess       ResponseCode = 200
	CodeBadRequest    ResponseCode = 400
	CodeUnauthorized  ResponseCode = 401
	CodeForbidden     ResponseCode = 403
	CodeNotFound      ResponseCode = 404
	CodeInternalError ResponseCode = 500
)

// ==================== 响应结构体 ====================

// Response 标准响应结构
type Response struct {
	Code      ResponseCode `json:"code"`
	Message   string       `json:"message"`
	Data      interface{}  `json:"data,omitempty"`
	Timestamp int64        `json:"timestamp"`
	RequestID string       `json:"request_id,omitempty"`
}

// PageResponse 分页响应结构
type PageResponse struct {
	Code      ResponseCode   `json:"code"`
	Message   string         `json:"message"`
	Data      interface{}    `json:"data,omitempty"`
	Timestamp int64          `json:"timestamp"`
	RequestID string         `json:"request_id,omitempty"`
	Pagination PaginationInfo `json:"pagination"`
}

// PaginationInfo 分页信息
type PaginationInfo struct {
	Page       int   `json:"page"`
	Size       int   `json:"size"`
	Total      int64 `json:"total"`
	TotalPages int   `json:"total_pages"`
	HasNext    bool  `json:"has_next"`
	HasPrev    bool  `json:"has_prev"`
}

// ==================== 响应构建器 ====================

// ResponseBuilder 响应构建器
type ResponseBuilder struct {
	response Response
}

// NewResponseBuilder 创建响应构建器
func NewResponseBuilder() *ResponseBuilder {
	return &ResponseBuilder{
		response: Response{
			Timestamp: time.Now().Unix(),
		},
	}
}

// Success 成功响应
func (rb *ResponseBuilder) Success(data interface{}) *ResponseBuilder {
	rb.response.Code = CodeSuccess
	rb.response.Message = "success"
	rb.response.Data = data
	return rb
}

// SuccessWithMessage 带消息的成功响应
func (rb *ResponseBuilder) SuccessWithMessage(data interface{}, message string) *ResponseBuilder {
	rb.response.Code = CodeSuccess
	rb.response.Message = message
	rb.response.Data = data
	return rb
}

// Fail 失败响应
func (rb *ResponseBuilder) Fail(message string) *ResponseBuilder {
	rb.response.Code = CodeBadRequest
	rb.response.Message = message
	rb.response.Data = nil
	return rb
}

// Error 错误响应
func (rb *ResponseBuilder) Error(code ResponseCode, message string) *ResponseBuilder {
	rb.response.Code = code
	rb.response.Message = message
	rb.response.Data = nil
	return rb
}

// WithRequestID 设置请求ID
func (rb *ResponseBuilder) WithRequestID(requestID string) *ResponseBuilder {
	rb.response.RequestID = requestID
	return rb
}

// Build 构建响应
func (rb *ResponseBuilder) Build() Response {
	return rb.response
}

// BuildPaged 构建分页响应
func (rb *ResponseBuilder) BuildPaged(page, size int, total int64) PageResponse {
	totalPages := int((total + int64(size) - 1) / int64(size))

	pagination := PaginationInfo{
		Page:       page,
		Size:       size,
		Total:      total,
		TotalPages: totalPages,
		HasNext:    page < totalPages,
		HasPrev:    page > 1,
	}

	return PageResponse{
		Code:       rb.response.Code,
		Message:    rb.response.Message,
		Data:       rb.response.Data,
		Timestamp:  rb.response.Timestamp,
		RequestID:  rb.response.RequestID,
		Pagination: pagination,
	}
}

// ==================== 便捷方法 ====================

// Success 成功响应
func Success(message string, data interface{}) Response {
	return NewResponseBuilder().SuccessWithMessage(data, message).Build()
}

// Fail 失败响应（400）
func Fail(message string) Response {
	return NewResponseBuilder().Fail(message).Build()
}

// BadRequest 错误请求（400）
func BadRequest(message string) Response {
	return NewResponseBuilder().Error(CodeBadRequest, message).Build()
}

// NotFound 资源不存在（404）
func NotFound(message string) Response {
	return NewResponseBuilder().Error(CodeNotFound, message).Build()
}

// Unauthorized 未授权（401）
func Unauthorized(message string) Response {
	return NewResponseBuilder().Error(CodeUnauthorized, message).Build()
}

// Forbidden 禁止访问（403）
func Forbidden(message string) Response {
	return NewResponseBuilder().Error(CodeForbidden, message).Build()
}

// InternalError 内部服务器错误（500）
func InternalError(message string) Response {
	return NewResponseBuilder().Error(CodeInternalError, message).Build()
}

// SuccessPage 分页成功响应
func SuccessPage(data interface{}, page, size int, total int64) PageResponse {
	return NewResponseBuilder().Success(data).BuildPaged(page, size, total)
}

// ==================== HTTP 状态码映射 ====================

// GetHTTPStatus 根据响应码获取HTTP状态码
func (r Response) GetHTTPStatus() int {
	switch r.Code {
	case CodeSuccess:
		return http.StatusOK
	case CodeBadRequest:
		return http.StatusBadRequest
	case CodeUnauthorized:
		return http.StatusUnauthorized
	case CodeForbidden:
		return http.StatusForbidden
	case CodeNotFound:
		return http.StatusNotFound
	case CodeInternalError:
		return http.StatusInternalServerError
	default:
		return http.StatusOK
	}
}

// GetHTTPStatus 根据响应码获取HTTP状态码
func (r PageResponse) GetHTTPStatus() int {
	switch r.Code {
	case CodeSuccess:
		return http.StatusOK
	case CodeBadRequest:
		return http.StatusBadRequest
	case CodeUnauthorized:
		return http.StatusUnauthorized
	case CodeForbidden:
		return http.StatusForbidden
	case CodeNotFound:
		return http.StatusNotFound
	case CodeInternalError:
		return http.StatusInternalServerError
	default:
		return http.StatusOK
	}
}

// ==================== 兼容性方法 ====================

// NewResponse 创建响应（兼容旧版本）
func NewResponse(code int, message string, data interface{}) Response {
	return Response{
		Code:      ResponseCode(code),
		Message:   message,
		Data:      data,
		Timestamp: time.Now().Unix(),
	}
}

// SuccessWithMessage 带消息的成功响应（兼容旧版本）
func SuccessWithMessage(data interface{}, message string) Response {
	return NewResponseBuilder().SuccessWithMessage(data, message).Build()
}
