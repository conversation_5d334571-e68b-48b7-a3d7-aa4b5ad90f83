package database

import (
	"context"
	"demo/config"
	"fmt"
	"log"
	"sync"
	"time"

	"github.com/chenmingyong0423/go-mongox/v2"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.mongodb.org/mongo-driver/mongo/readpref"
)

var (
	mongoClient *mongo.Client
	mongoxClient *mongox.Client
	once sync.Once
	initErr error
)

// InitMongoDB 初始化 MongoDB 连接
func InitMongoDB() error {
	once.Do(func() {
		cfg := config.AppConfig.MongoDB
		
		// 设置连接选项
		clientOptions := options.Client().
			ApplyURI(cfg.GetConnectionString()).
			SetMaxPoolSize(100).                    // 最大连接池大小
			SetMinPoolSize(10).                     // 最小连接池大小
			SetMaxConnIdleTime(30 * time.Second).   // 连接最大空闲时间
			SetConnectTimeout(10 * time.Second).    // 连接超时
			SetServerSelectionTimeout(5 * time.Second) // 服务器选择超时

		// 如果有认证信息
		if cfg.Username != "" && cfg.Password != "" {
			clientOptions.SetAuth(options.Credential{
				Username: cfg.Username,
				Password: cfg.Password,
			})
		}

		// 创建连接
		client, err := mongo.Connect(clientOptions)
		if err != nil {
			initErr = fmt.Errorf("failed to connect to MongoDB: %v", err)
			return
		}

		// 测试连接
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()
		
		if err := client.Ping(ctx, readpref.Primary()); err != nil {
			initErr = fmt.Errorf("failed to ping MongoDB: %v", err)
			return
		}

		mongoClient = client
		mongoxClient = mongox.NewClient(client, &mongox.Config{})
		
		log.Println("MongoDB connected successfully")
	})
	
	return initErr
}

// GetMongoClient 获取原生 MongoDB 客户端
func GetMongoClient() *mongo.Client {
	if mongoClient == nil {
		if err := InitMongoDB(); err != nil {
			log.Fatal("Failed to initialize MongoDB:", err)
		}
	}
	return mongoClient
}

// GetMongoxClient 获取 Mongox 客户端
func GetMongoxClient() *mongox.Client {
	if mongoxClient == nil {
		if err := InitMongoDB(); err != nil {
			log.Fatal("Failed to initialize MongoDB:", err)
		}
	}
	return mongoxClient
}

// GetDatabase 获取数据库实例
func GetDatabase() *mongox.Database {
	client := GetMongoxClient()
	return client.NewDatabase(config.AppConfig.MongoDB.DBName)
}

// GetCollection 获取集合实例
func GetCollection[T any](collectionName string) *mongox.Collection[T] {
	database := GetDatabase()
	return mongox.NewCollection[T](database, collectionName)
}

// Close 关闭数据库连接
func Close() error {
	if mongoClient != nil {
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()
		return mongoClient.Disconnect(ctx)
	}
	return nil
}
