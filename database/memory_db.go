package database

import (
	"demo/response"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"go.mongodb.org/mongo-driver/v2/bson"
)

// MemoryDB 内存数据库实现
type MemoryDB struct {
	collections map[string]map[string]interface{}
	idCounter   int
	mutex       sync.RWMutex
}

// NewMemoryDB 创建新的内存数据库
func NewMemoryDB() *MemoryDB {
	return &MemoryDB{
		collections: make(map[string]map[string]interface{}),
		idCounter:   0,
	}
}

// Create 创建文档
func (db *MemoryDB) Create(collectionName string, entity interface{}) (interface{}, error) {
	db.mutex.Lock()
	defer db.mutex.Unlock()

	// 确保集合存在
	if db.collections[collectionName] == nil {
		db.collections[collectionName] = make(map[string]interface{})
	}

	// 生成ID并设置到实体
	id := db.generateID()
	db.setEntityID(entity, id)
	db.setTimestamps(entity, true)

	// 存储实体
	db.collections[collectionName][id] = entity

	return id, nil
}

// GetByID 根据ID获取文档
func (db *MemoryDB) GetByID(collectionName string, id string, result interface{}) error {
	db.mutex.RLock()
	defer db.mutex.RUnlock()

	collection := db.collections[collectionName]
	if collection == nil {
		return fmt.Errorf("entity not found")
	}

	entity, exists := collection[id]
	if !exists {
		return fmt.Errorf("entity not found")
	}

	// 复制数据到result
	return db.copyEntity(entity, result)
}

// Update 更新文档
func (db *MemoryDB) Update(collectionName string, id string, update interface{}) error {
	db.mutex.Lock()
	defer db.mutex.Unlock()

	collection := db.collections[collectionName]
	if collection == nil {
		return fmt.Errorf("entity not found")
	}

	entity, exists := collection[id]
	if !exists {
		return fmt.Errorf("entity not found")
	}

	// 更新字段
	db.updateEntity(entity, update)
	db.setTimestamps(entity, false)

	return nil
}

// Delete 删除文档
func (db *MemoryDB) Delete(collectionName string, id string) error {
	db.mutex.Lock()
	defer db.mutex.Unlock()

	collection := db.collections[collectionName]
	if collection == nil {
		return fmt.Errorf("entity not found")
	}

	if _, exists := collection[id]; !exists {
		return fmt.Errorf("entity not found")
	}

	delete(collection, id)
	return nil
}

// QueryWithPagination 分页查询
func (db *MemoryDB) QueryWithPagination(collectionName string, params map[string]interface{}) (*response.PagedResponse, error) {
	db.mutex.RLock()
	defer db.mutex.RUnlock()

	collection := db.collections[collectionName]
	if collection == nil {
		collection = make(map[string]interface{})
	}

	// 解析分页参数
	page := 1
	size := 10

	if p, ok := params["page"]; ok {
		if pInt, err := strconv.Atoi(fmt.Sprintf("%v", p)); err == nil && pInt > 0 {
			page = pInt
		}
	}
	if s, ok := params["size"]; ok {
		if sInt, err := strconv.Atoi(fmt.Sprintf("%v", s)); err == nil && sInt > 0 && sInt <= 1000 {
			size = sInt
		}
	}

	// 获取所有数据
	var allData []interface{}
	for _, entity := range collection {
		allData = append(allData, entity)
	}

	// 应用过滤器（简单实现）
	filteredData := db.applyFilters(allData, params)

	// 排序（简单实现）
	// 这里可以添加更复杂的排序逻辑

	// 分页
	total := len(filteredData)
	start := (page - 1) * size
	end := start + size

	if start > total {
		start = total
	}
	if end > total {
		end = total
	}

	var pageData []interface{}
	if start < end {
		pageData = filteredData[start:end]
	}

	totalPages := (total + size - 1) / size

	return &response.PagedResponse{
		StandardResponse: response.StandardResponse{
			Code:      response.CodeSuccess,
			Message:   "success",
			Data:      pageData,
			Timestamp: time.Now().Unix(),
		},
		Pagination: response.PaginationInfo{
			Page:       page,
			Size:       size,
			Total:      int64(total),
			TotalPages: totalPages,
			HasNext:    page < totalPages,
			HasPrev:    page > 1,
		},
	}, nil
}

// Close 关闭数据库连接
func (db *MemoryDB) Close() error {
	// 内存数据库不需要关闭操作
	return nil
}

// generateID 生成ID
func (db *MemoryDB) generateID() string {
	db.idCounter++
	return bson.NewObjectID().Hex()
}

// setEntityID 设置实体ID
func (db *MemoryDB) setEntityID(entity interface{}, id string) {
	v := reflect.ValueOf(entity)
	if v.Kind() == reflect.Ptr {
		v = v.Elem()
	}

	if v.Kind() == reflect.Struct {
		idField := v.FieldByName("ID")
		if idField.IsValid() && idField.CanSet() {
			if objectID, err := bson.ObjectIDFromHex(id); err == nil {
				idField.Set(reflect.ValueOf(objectID))
			}
		}
	}
}

// setTimestamps 设置时间戳
func (db *MemoryDB) setTimestamps(entity interface{}, isCreate bool) {
	v := reflect.ValueOf(entity)
	if v.Kind() == reflect.Ptr {
		v = v.Elem()
	}

	if v.Kind() == reflect.Struct {
		now := time.Now()

		if isCreate {
			if createdAtField := v.FieldByName("CreatedAt"); createdAtField.IsValid() && createdAtField.CanSet() {
				createdAtField.Set(reflect.ValueOf(now))
			}
		}

		if updatedAtField := v.FieldByName("UpdatedAt"); updatedAtField.IsValid() && updatedAtField.CanSet() {
			updatedAtField.Set(reflect.ValueOf(now))
		}
	}
}

// copyEntity 复制实体
func (db *MemoryDB) copyEntity(src, dst interface{}) error {
	srcVal := reflect.ValueOf(src)
	dstVal := reflect.ValueOf(dst)

	if dstVal.Kind() != reflect.Ptr {
		return fmt.Errorf("destination must be a pointer")
	}

	dstVal = dstVal.Elem()
	if srcVal.Kind() == reflect.Ptr {
		srcVal = srcVal.Elem()
	}

	if srcVal.Type() != dstVal.Type() {
		return fmt.Errorf("type mismatch")
	}

	dstVal.Set(srcVal)
	return nil
}

// updateEntity 更新实体
func (db *MemoryDB) updateEntity(entity, update interface{}) {
	entityVal := reflect.ValueOf(entity)
	updateVal := reflect.ValueOf(update)

	if entityVal.Kind() == reflect.Ptr {
		entityVal = entityVal.Elem()
	}
	if updateVal.Kind() == reflect.Ptr {
		updateVal = updateVal.Elem()
	}

	if entityVal.Kind() == reflect.Struct && updateVal.Kind() == reflect.Struct {
		for i := 0; i < updateVal.NumField(); i++ {
			updateField := updateVal.Field(i)
			updateFieldType := updateVal.Type().Field(i)

			if updateField.IsValid() && !updateField.IsZero() {
				entityField := entityVal.FieldByName(updateFieldType.Name)
				if entityField.IsValid() && entityField.CanSet() {
					entityField.Set(updateField)
				}
			}
		}
	}
}

// applyFilters 应用过滤器
func (db *MemoryDB) applyFilters(data []interface{}, params map[string]interface{}) []interface{} {
	var filtered []interface{}

	for _, item := range data {
		if db.matchesFilters(item, params) {
			filtered = append(filtered, item)
		}
	}

	return filtered
}

// matchesFilters 检查是否匹配过滤器
func (db *MemoryDB) matchesFilters(item interface{}, params map[string]interface{}) bool {
	itemVal := reflect.ValueOf(item)
	if itemVal.Kind() == reflect.Ptr {
		itemVal = itemVal.Elem()
	}

	for key, value := range params {
		if strings.HasSuffix(key, "__eq") {
			fieldName := strings.TrimSuffix(key, "__eq")
			if !db.fieldEquals(itemVal, fieldName, value) {
				return false
			}
		} else if strings.HasSuffix(key, "__like") {
			fieldName := strings.TrimSuffix(key, "__like")
			if !db.fieldContains(itemVal, fieldName, fmt.Sprintf("%v", value)) {
				return false
			}
		}
	}

	return true
}

// fieldEquals 检查字段是否相等
func (db *MemoryDB) fieldEquals(itemVal reflect.Value, fieldName string, value interface{}) bool {
	if itemVal.Kind() == reflect.Struct {
		field := itemVal.FieldByName(strings.Title(fieldName))
		if field.IsValid() {
			return reflect.DeepEqual(field.Interface(), value)
		}
	}
	return true
}

// fieldContains 检查字段是否包含值
func (db *MemoryDB) fieldContains(itemVal reflect.Value, fieldName string, value string) bool {
	if itemVal.Kind() == reflect.Struct {
		field := itemVal.FieldByName(strings.Title(fieldName))
		if field.IsValid() && field.Kind() == reflect.String {
			return strings.Contains(strings.ToLower(field.String()), strings.ToLower(value))
		}
	}
	return true
}
