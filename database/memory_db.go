package database

import (
	"demo/response"
	"fmt"
	"log"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"go.mongodb.org/mongo-driver/v2/bson/primitive"
)

// MemoryDB 内存数据库
type MemoryDB struct {
	collections map[string]map[string]interface{}
	mutex       sync.RWMutex
	idCounter   int64
}

var memDB *MemoryDB

// InitDB 初始化内存数据库
func InitDB() error {
	memDB = &MemoryDB{
		collections: make(map[string]map[string]interface{}),
		mutex:       sync.RWMutex{},
		idCounter:   1,
	}
	
	log.Println("Memory database initialized successfully")
	return nil
}

// CloseDB 关闭数据库连接
func CloseDB() error {
	log.Println("Memory database closed")
	return nil
}

// generateID 生成新的ID
func (db *MemoryDB) generateID() string {
	db.idCounter++
	return primitive.NewObjectID().Hex()
}

// Create 创建文档
func Create(collectionName string, entity interface{}) (interface{}, error) {
	memDB.mutex.Lock()
	defer memDB.mutex.Unlock()
	
	if memDB.collections[collectionName] == nil {
		memDB.collections[collectionName] = make(map[string]interface{})
	}
	
	// 生成ID
	id := memDB.generateID()
	
	// 使用反射设置ID字段
	v := reflect.ValueOf(entity)
	if v.Kind() == reflect.Ptr {
		v = v.Elem()
	}
	
	if v.Kind() == reflect.Struct {
		idField := v.FieldByName("ID")
		if idField.IsValid() && idField.CanSet() {
			if objectID, err := primitive.ObjectIDFromHex(id); err == nil {
				idField.Set(reflect.ValueOf(objectID))
			}
		}
		
		// 设置时间戳
		now := time.Now()
		if createdAtField := v.FieldByName("CreatedAt"); createdAtField.IsValid() && createdAtField.CanSet() {
			createdAtField.Set(reflect.ValueOf(now))
		}
		if updatedAtField := v.FieldByName("UpdatedAt"); updatedAtField.IsValid() && updatedAtField.CanSet() {
			updatedAtField.Set(reflect.ValueOf(now))
		}
	}
	
	memDB.collections[collectionName][id] = entity
	
	return id, nil
}

// GetByID 根据ID获取文档
func GetByID(collectionName string, id string, result interface{}) error {
	memDB.mutex.RLock()
	defer memDB.mutex.RUnlock()
	
	collection, exists := memDB.collections[collectionName]
	if !exists {
		return fmt.Errorf("entity not found")
	}
	
	entity, exists := collection[id]
	if !exists {
		return fmt.Errorf("entity not found")
	}
	
	// 复制数据到result
	resultValue := reflect.ValueOf(result)
	if resultValue.Kind() != reflect.Ptr {
		return fmt.Errorf("result must be a pointer")
	}
	
	entityValue := reflect.ValueOf(entity)
	if entityValue.Kind() == reflect.Ptr {
		entityValue = entityValue.Elem()
	}
	
	resultValue.Elem().Set(entityValue)
	
	return nil
}

// Update 更新文档
func Update(collectionName string, id string, update interface{}) error {
	memDB.mutex.Lock()
	defer memDB.mutex.Unlock()
	
	collection, exists := memDB.collections[collectionName]
	if !exists {
		return fmt.Errorf("entity not found")
	}
	
	if _, exists := collection[id]; !exists {
		return fmt.Errorf("entity not found")
	}
	
	// 设置更新时间
	v := reflect.ValueOf(update)
	if v.Kind() == reflect.Ptr {
		v = v.Elem()
	}
	
	if v.Kind() == reflect.Struct {
		if updatedAtField := v.FieldByName("UpdatedAt"); updatedAtField.IsValid() && updatedAtField.CanSet() {
			updatedAtField.Set(reflect.ValueOf(time.Now()))
		}
	}
	
	collection[id] = update
	
	return nil
}

// Delete 删除文档
func Delete(collectionName string, id string) error {
	memDB.mutex.Lock()
	defer memDB.mutex.Unlock()
	
	collection, exists := memDB.collections[collectionName]
	if !exists {
		return fmt.Errorf("entity not found")
	}
	
	if _, exists := collection[id]; !exists {
		return fmt.Errorf("entity not found")
	}
	
	delete(collection, id)
	
	return nil
}

// QueryWithPagination 分页查询
func QueryWithPagination(collectionName string, params map[string]interface{}) (*response.PagedResponse, error) {
	memDB.mutex.RLock()
	defer memDB.mutex.RUnlock()
	
	// 提取分页参数
	page := 1
	size := 10
	order := "-_id"
	
	if p, ok := params["page"]; ok {
		if pInt, err := strconv.Atoi(fmt.Sprintf("%v", p)); err == nil && pInt > 0 {
			page = pInt
		}
	}
	if s, ok := params["size"]; ok {
		if sInt, err := strconv.Atoi(fmt.Sprintf("%v", s)); err == nil && sInt > 0 && sInt <= 1000 {
			size = sInt
		}
	}
	if o, ok := params["order"].(string); ok && o != "" {
		order = o
	}
	
	collection, exists := memDB.collections[collectionName]
	if !exists {
		collection = make(map[string]interface{})
	}
	
	// 收集所有匹配的实体
	var allResults []interface{}
	for _, entity := range collection {
		if matchesFilter(entity, params) {
			allResults = append(allResults, entity)
		}
	}
	
	// 排序（简化版本，只支持按ID排序）
	if strings.HasPrefix(order, "-") {
		// 降序，这里简化处理
	}
	
	// 分页
	total := int64(len(allResults))
	start := (page - 1) * size
	end := start + size
	
	if start >= len(allResults) {
		allResults = []interface{}{}
	} else if end > len(allResults) {
		allResults = allResults[start:]
	} else {
		allResults = allResults[start:end]
	}
	
	// 构建响应
	pageResponse := response.SuccessPage(allResults, page, size, total)
	return &pageResponse, nil
}

// matchesFilter 检查实体是否匹配过滤条件
func matchesFilter(entity interface{}, params map[string]interface{}) bool {
	v := reflect.ValueOf(entity)
	if v.Kind() == reflect.Ptr {
		v = v.Elem()
	}
	
	if v.Kind() != reflect.Struct {
		return true
	}
	
	for key, value := range params {
		if key == "page" || key == "size" || key == "order" || value == nil {
			continue
		}
		
		// 处理字段名
		fieldName := key
		isLike := false
		if strings.HasSuffix(key, "__like") {
			fieldName = strings.TrimSuffix(key, "__like")
			isLike = true
		} else if strings.HasSuffix(key, "__eq") {
			fieldName = strings.TrimSuffix(key, "__eq")
		}
		
		// 首字母大写
		fieldName = strings.Title(fieldName)
		
		field := v.FieldByName(fieldName)
		if !field.IsValid() {
			continue
		}
		
		fieldValue := field.Interface()
		strFieldValue := fmt.Sprintf("%v", fieldValue)
		strParamValue := fmt.Sprintf("%v", value)
		
		if isLike {
			if !strings.Contains(strings.ToLower(strFieldValue), strings.ToLower(strParamValue)) {
				return false
			}
		} else {
			if strFieldValue != strParamValue {
				return false
			}
		}
	}
	
	return true
}
