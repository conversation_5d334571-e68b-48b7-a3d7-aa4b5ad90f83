package database

import (
	"fmt"
	"reflect"
	"regexp"
	"strings"
	"time"

	"github.com/chenmingyong0423/go-mongox/v2/builder/query"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// QueryOperator 查询操作符
type QueryOperator string

const (
	OpEqual    QueryOperator = "__eq"
	OpNotEqual QueryOperator = "__neq"
	OpGreater  QueryOperator = "__gt"
	OpLess     QueryOperator = "__lt"
	OpIn       QueryOperator = "__in"
	OpNotIn    QueryOperator = "__nin"
	OpLike     QueryOperator = "__like"
	OpDateGt   QueryOperator = "__dgt"
	OpDateLt   QueryOperator = "__dlt"
	OpRegex    QueryOperator = "__regex"
)

// QueryBuilder 查询构建器
type QueryBuilder struct {
	builder *query.Builder
	errors  []error
}

// NewQueryBuilder 创建新的查询构建器
func NewQueryBuilder() *QueryBuilder {
	return &QueryBuilder{
		builder: query.NewBuilder(),
		errors:  make([]error, 0),
	}
}

// BuildFromMap 从 map 构建查询
func (qb *QueryBuilder) BuildFromMap(params map[string]interface{}) *query.Builder {
	for key, value := range params {
		if qb.isSystemField(key) || value == nil {
			continue
		}

		qb.processField(key, value)
	}

	return qb.builder
}

// processField 处理单个字段
func (qb *QueryBuilder) processField(key string, value interface{}) {
	// 处理 ID 字段
	if qb.isIDField(key) {
		qb.handleIDField(key, value)
		return
	}

	// 处理操作符字段
	if operator, realKey := qb.extractOperator(key); operator != "" {
		qb.handleOperatorField(realKey, operator, value)
		return
	}

	// 处理普通字段
	qb.handleNormalField(key, value)
}

// isSystemField 判断是否为系统字段
func (qb *QueryBuilder) isSystemField(key string) bool {
	systemFields := []string{"page", "size", "order"}
	for _, field := range systemFields {
		if key == field {
			return true
		}
	}
	return false
}

// isIDField 判断是否为 ID 字段
func (qb *QueryBuilder) isIDField(key string) bool {
	return key == "id" || key == "_id" || strings.HasSuffix(key, "_id")
}

// handleIDField 处理 ID 字段
func (qb *QueryBuilder) handleIDField(key string, value interface{}) {
	strValue, ok := value.(string)
	if !ok || strValue == "" {
		return
	}

	objectID, err := bson.ObjectIDFromHex(strValue)
	if err != nil {
		qb.errors = append(qb.errors, fmt.Errorf("invalid ObjectID for field %s: %v", key, err))
		return
	}

	if key == "id" || key == "_id" {
		qb.builder = qb.builder.Id(objectID)
	} else {
		qb.builder = qb.builder.Eq(key, objectID)
	}
}

// extractOperator 提取操作符
func (qb *QueryBuilder) extractOperator(key string) (QueryOperator, string) {
	operators := []QueryOperator{OpEqual, OpNotEqual, OpGreater, OpLess, OpIn, OpNotIn, OpLike, OpDateGt, OpDateLt, OpRegex}

	for _, op := range operators {
		if strings.HasSuffix(key, string(op)) {
			realKey := strings.TrimSuffix(key, string(op))
			return op, realKey
		}
	}

	return "", key
}

// handleOperatorField 处理带操作符的字段
func (qb *QueryBuilder) handleOperatorField(key string, operator QueryOperator, value interface{}) {
	switch operator {
	case OpEqual:
		if value != "" {
			qb.builder = qb.builder.Eq(key, value)
		}
	case OpNotEqual:
		qb.builder = qb.builder.Ne(key, value)
	case OpGreater:
		qb.builder = qb.builder.Gt(key, value)
	case OpLess:
		qb.builder = qb.builder.Lt(key, value)
	case OpDateGt:
		if date, err := qb.parseDate(value); err == nil {
			qb.builder = qb.builder.Gt(key, date)
		}
	case OpDateLt:
		if date, err := qb.parseDate(value); err == nil {
			qb.builder = qb.builder.Lt(key, date)
		}
	case OpIn:
		if arr, ok := value.([]interface{}); ok && len(arr) > 0 {
			qb.builder = qb.builder.In(key, arr)
		}
	case OpLike:
		if str, ok := value.(string); ok && str != "" {
			qb.builder = qb.builder.RegexOptions(key, regexp.QuoteMeta(str), "i")
		}
	case OpRegex:
		if str, ok := value.(string); ok && str != "" {
			qb.builder = qb.builder.Regex(key, str)
		}
	}
}

// handleNormalField 处理普通字段
func (qb *QueryBuilder) handleNormalField(key string, value interface{}) {
	if reflect.TypeOf(value).Kind() == reflect.String {
		strValue := value.(string)
		if strValue == "" {
			return
		}

		// 对于字符串字段，默认使用模糊匹配
		qb.builder = qb.builder.RegexOptions(key, regexp.QuoteMeta(strValue), "i")
	} else {
		qb.builder = qb.builder.Eq(key, value)
	}
}

// parseDate 解析日期
func (qb *QueryBuilder) parseDate(value interface{}) (time.Time, error) {
	str, ok := value.(string)
	if !ok {
		return time.Time{}, fmt.Errorf("date value must be string")
	}

	layouts := []string{
		"2006-01-02 15:04:05",
		"2006-01-02",
		time.RFC3339,
	}

	for _, layout := range layouts {
		if date, err := time.Parse(layout, str); err == nil {
			return date, nil
		}
	}

	return time.Time{}, fmt.Errorf("unable to parse date: %s", str)
}

// GetErrors 获取构建过程中的错误
func (qb *QueryBuilder) GetErrors() []error {
	return qb.errors
}

// HasErrors 检查是否有错误
func (qb *QueryBuilder) HasErrors() bool {
	return len(qb.errors) > 0
}
