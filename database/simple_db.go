package database

import (
	"context"
	"demo/config"
	"demo/response"
	"fmt"
	"log"
	"strings"

	"github.com/chenmingyong0423/go-mongox/v2"
	"github.com/chenmingyong0423/go-mongox/v2/bsonx"
	"github.com/chenmingyong0423/go-mongox/v2/builder/query"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.mongodb.org/mongo-driver/mongo/readpref"
)

var (
	mongoClient  *mongo.Client
	mongoxClient *mongox.Client
)

// InitDB 初始化数据库连接
func InitDB() error {
	cfg := config.AppConfig.MongoDB
	
	// 构建连接字符串
	uri := fmt.Sprintf("mongodb://%s:%s@%s:%d", cfg.Username, cfg.Password, cfg.Host, cfg.Port)
	
	// 创建客户端选项
	clientOptions := options.Client().ApplyURI(uri)
	
	// 连接到MongoDB
	client, err := mongo.Connect(context.Background(), clientOptions)
	if err != nil {
		return fmt.Errorf("failed to connect to MongoDB: %v", err)
	}
	
	// 测试连接
	if err := client.Ping(context.Background(), readpref.Primary()); err != nil {
		return fmt.Errorf("failed to ping MongoDB: %v", err)
	}
	
	mongoClient = client
	// 暂时不使用mongox，直接使用原生MongoDB驱动
	// mongoxClient = mongox.NewClient(client, &mongox.Config{})
	
	log.Println("MongoDB connected successfully")
	return nil
}

// GetCollection 获取集合
func GetCollection[T any](collectionName string) *mongox.Collection[T] {
	if mongoxClient == nil {
		log.Fatal("Database not initialized")
	}
	database := mongoxClient.NewDatabase(config.AppConfig.MongoDB.DBName)
	return mongox.NewCollection[T](database, collectionName)
}

// CloseDB 关闭数据库连接
func CloseDB() error {
	if mongoClient != nil {
		return mongoClient.Disconnect(context.Background())
	}
	return nil
}

// SimpleQueryWithPagination 简单的分页查询
func SimpleQueryWithPagination[T any](collectionName string, params map[string]interface{}) (*response.PagedResponse, error) {
	// 提取分页参数
	page := 1
	size := 10
	order := "-_id"
	
	if p, ok := params["page"].(int); ok && p > 0 {
		page = p
	}
	if s, ok := params["size"].(int); ok && s > 0 && s <= 1000 {
		size = s
	}
	if o, ok := params["order"].(string); ok && o != "" {
		order = o
	}
	
	// 构建查询条件
	queryBuilder := query.NewBuilder()
	
	// 处理查询参数
	for key, value := range params {
		if key == "page" || key == "size" || key == "order" || value == nil {
			continue
		}
		
		// 处理ID字段
		if key == "id" || key == "_id" {
			if strValue, ok := value.(string); ok && strValue != "" {
				if objectID, err := primitive.ObjectIDFromHex(strValue); err == nil {
					queryBuilder = queryBuilder.Id(objectID)
				}
			}
			continue
		}
		
		// 处理字符串字段的模糊查询
		if strValue, ok := value.(string); ok && strValue != "" {
			if strings.HasSuffix(key, "__like") {
				realKey := strings.TrimSuffix(key, "__like")
				queryBuilder = queryBuilder.RegexOptions(realKey, strValue, "i")
			} else if strings.HasSuffix(key, "__eq") {
				realKey := strings.TrimSuffix(key, "__eq")
				queryBuilder = queryBuilder.Eq(realKey, strValue)
			} else {
				// 默认使用模糊查询
				queryBuilder = queryBuilder.RegexOptions(key, strValue, "i")
			}
		} else {
			queryBuilder = queryBuilder.Eq(key, value)
		}
	}
	
	// 构建排序
	sortBsonx := bsonx.NewD()
	orderFields := strings.Split(order, ",")
	for _, field := range orderFields {
		field = strings.TrimSpace(field)
		direction := 1
		if strings.HasPrefix(field, "-") {
			direction = -1
			field = strings.TrimPrefix(field, "-")
		}
		sortBsonx = sortBsonx.Add(field, direction)
	}
	
	// 获取集合
	collection := GetCollection[T](collectionName)
	
	// 执行查询
	skip := int64(size * (page - 1))
	limit := int64(size)
	
	results, err := collection.Finder().
		Filter(queryBuilder.Build()).
		Sort(sortBsonx).
		Skip(skip).
		Limit(limit).
		Find(context.Background())
	if err != nil {
		return nil, fmt.Errorf("failed to execute query: %v", err)
	}
	
	// 获取总数
	total, err := collection.Finder().Filter(queryBuilder.Build()).Count(context.Background())
	if err != nil {
		return nil, fmt.Errorf("failed to count documents: %v", err)
	}
	
	// 构建响应
	pageResponse := response.SuccessPage(results, page, size, total)
	return &pageResponse, nil
}

// SimpleCreate 简单的创建操作
func SimpleCreate[T any](collectionName string, entity *T) (*T, error) {
	collection := GetCollection[T](collectionName)
	
	result, err := collection.Creator().InsertOne(context.Background(), entity)
	if err != nil {
		return nil, fmt.Errorf("failed to create entity: %v", err)
	}
	
	return result, nil
}

// SimpleGetByID 根据ID获取实体
func SimpleGetByID[T any](collectionName string, id string) (*T, error) {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, fmt.Errorf("invalid ID format: %v", err)
	}
	
	collection := GetCollection[T](collectionName)
	result, err := collection.Finder().Filter(query.Id(objectID).Build()).FindOne(context.Background())
	if err != nil {
		return nil, fmt.Errorf("entity not found: %v", err)
	}
	
	return result, nil
}

// SimpleUpdate 简单的更新操作
func SimpleUpdate[T any](collectionName string, id string, entity *T) (*T, error) {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, fmt.Errorf("invalid ID format: %v", err)
	}
	
	collection := GetCollection[T](collectionName)
	
	// 这里简化更新逻辑，直接替换整个文档
	_, err = collection.Updater().Filter(query.Id(objectID).Build()).ReplaceOne(context.Background(), entity)
	if err != nil {
		return nil, fmt.Errorf("failed to update entity: %v", err)
	}
	
	// 返回更新后的实体
	return SimpleGetByID[T](collectionName, id)
}

// SimpleDelete 简单的删除操作
func SimpleDelete[T any](collectionName string, id string) error {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return fmt.Errorf("invalid ID format: %v", err)
	}
	
	collection := GetCollection[T](collectionName)
	result, err := collection.Deleter().Filter(query.Id(objectID).Build()).DeleteOne(context.Background())
	if err != nil {
		return fmt.Errorf("failed to delete entity: %v", err)
	}
	
	if result.DeletedCount == 0 {
		return fmt.Errorf("entity not found")
	}
	
	return nil
}
