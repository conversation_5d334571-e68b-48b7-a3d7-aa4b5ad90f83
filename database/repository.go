package database

import (
	"context"

	"github.com/chenmingyong0423/go-mongox/v2/bsonx"
	"go.mongodb.org/mongo-driver/v2/bson"
	"go.mongodb.org/mongo-driver/v2/mongo/options"
)

// Repository 数据仓库 - 纯数据访问层，不涉及业务逻辑
type Repository[T any] struct {
	collectionName string
}

// NewRepository 创建数据仓库
func NewRepository[T any](collectionName string) *Repository[T] {
	return &Repository[T]{
		collectionName: collectionName,
	}
}

// FindWithPagination 分页查询 - 纯数据访问，只返回数据和总数
func (r *Repository[T]) FindWithPagination(
	ctx context.Context,
	filter bson.M,
	skip, limit int64,
	sort *bsonx.D,
) ([]T, int64, error) {
	// 获取集合
	collection := GetCollection[T](r.collectionName)

	// 执行分页查询
	results, err := collection.Finder().
		Filter(filter).
		Find(ctx,
			options.Find().SetSort(sort),
			options.Find().SetSkip(skip),
			options.Find().SetLimit(limit),
		)
	if err != nil {
		return nil, 0, err
	}

	// 获取总数
	total, err := collection.Finder().Filter(filter).Count(ctx)
	if err != nil {
		return nil, 0, err
	}

	return results, total, nil
}

// FindAll 查询所有记录 - 纯数据访问
func (r *Repository[T]) FindAll(
	ctx context.Context,
	filter bson.M,
) ([]T, error) {
	// 获取集合
	collection := GetCollection[T](r.collectionName)

	// 执行查询
	results, err := collection.Finder().Filter(filter).Find(ctx)
	if err != nil {
		return nil, err
	}

	return results, nil
}

// FindOne 查询单个记录 - 纯数据访问
func (r *Repository[T]) FindOne(
	ctx context.Context,
	filter bson.M,
) (*T, error) {
	// 获取集合
	collection := GetCollection[T](r.collectionName)

	// 执行查询
	result, err := collection.Finder().Filter(filter).FindOne(ctx)
	if err != nil {
		return nil, err
	}

	return result, nil
}

// Create 创建记录 - 纯数据访问
func (r *Repository[T]) Create(
	ctx context.Context,
	entity *T,
) (*T, error) {
	collection := GetCollection[T](r.collectionName)
	return collection.Creator().InsertOne(ctx, entity)
}

// UpdateByFilter 根据过滤条件更新 - 纯数据访问
func (r *Repository[T]) UpdateByFilter(
	ctx context.Context,
	filter bson.M,
	update bson.M,
) (int64, error) {
	collection := GetCollection[T](r.collectionName)
	
	result, err := collection.Updater().Filter(filter).Updates(update).UpdateOne(ctx)
	if err != nil {
		return 0, err
	}
	
	return result.MatchedCount, nil
}

// DeleteByFilter 根据过滤条件删除 - 纯数据访问
func (r *Repository[T]) DeleteByFilter(
	ctx context.Context,
	filter bson.M,
) (int64, error) {
	collection := GetCollection[T](r.collectionName)
	
	result, err := collection.Deleter().Filter(filter).DeleteOne(ctx)
	if err != nil {
		return 0, err
	}
	
	return result.DeletedCount, nil
}

// Count 计数 - 纯数据访问
func (r *Repository[T]) Count(
	ctx context.Context,
	filter bson.M,
) (int64, error) {
	collection := GetCollection[T](r.collectionName)
	return collection.Finder().Filter(filter).Count(ctx)
}
