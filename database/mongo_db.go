package database

import (
	"context"
	"demo/config"
	"demo/response"
	"fmt"
	"log"
	"strings"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.mongodb.org/mongo-driver/mongo/readpref"
)

var (
	client   *mongo.Client
	database *mongo.Database
)

// InitDB 初始化数据库连接
func InitDB() error {
	cfg := config.AppConfig.MongoDB

	// 构建连接字符串
	var uri string
	if cfg.Username != "" && cfg.Password != "" {
		uri = fmt.Sprintf("mongodb://%s:%s@%s:%d", cfg.Username, cfg.Password, cfg.Host, cfg.Port)
	} else {
		uri = fmt.Sprintf("mongodb://%s:%d", cfg.Host, cfg.Port)
	}
	
	// 创建客户端选项
	clientOptions := options.Client().ApplyURI(uri)
	
	// 连接到MongoDB
	var err error
	client, err = mongo.Connect(context.Background(), clientOptions)
	if err != nil {
		return fmt.Errorf("failed to connect to MongoDB: %v", err)
	}
	
	// 测试连接
	if err := client.Ping(context.Background(), readpref.Primary()); err != nil {
		return fmt.Errorf("failed to ping MongoDB: %v", err)
	}
	
	database = client.Database(cfg.DBName)
	
	log.Println("MongoDB connected successfully")
	return nil
}

// GetCollection 获取集合
func GetCollection(collectionName string) *mongo.Collection {
	if database == nil {
		log.Fatal("Database not initialized")
	}
	return database.Collection(collectionName)
}

// CloseDB 关闭数据库连接
func CloseDB() error {
	if client != nil {
		return client.Disconnect(context.Background())
	}
	return nil
}

// Create 创建文档
func Create(collectionName string, entity interface{}) (interface{}, error) {
	collection := GetCollection(collectionName)
	
	result, err := collection.InsertOne(context.Background(), entity)
	if err != nil {
		return nil, fmt.Errorf("failed to create entity: %v", err)
	}
	
	// 返回插入的ID
	return result.InsertedID, nil
}

// GetByID 根据ID获取文档
func GetByID(collectionName string, id string, result interface{}) error {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return fmt.Errorf("invalid ID format: %v", err)
	}
	
	collection := GetCollection(collectionName)
	err = collection.FindOne(context.Background(), bson.M{"_id": objectID}).Decode(result)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return fmt.Errorf("entity not found")
		}
		return fmt.Errorf("failed to get entity: %v", err)
	}
	
	return nil
}

// Update 更新文档
func Update(collectionName string, id string, update interface{}) error {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return fmt.Errorf("invalid ID format: %v", err)
	}
	
	collection := GetCollection(collectionName)
	result, err := collection.ReplaceOne(context.Background(), bson.M{"_id": objectID}, update)
	if err != nil {
		return fmt.Errorf("failed to update entity: %v", err)
	}
	
	if result.MatchedCount == 0 {
		return fmt.Errorf("entity not found")
	}
	
	return nil
}

// Delete 删除文档
func Delete(collectionName string, id string) error {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return fmt.Errorf("invalid ID format: %v", err)
	}
	
	collection := GetCollection(collectionName)
	result, err := collection.DeleteOne(context.Background(), bson.M{"_id": objectID})
	if err != nil {
		return fmt.Errorf("failed to delete entity: %v", err)
	}
	
	if result.DeletedCount == 0 {
		return fmt.Errorf("entity not found")
	}
	
	return nil
}

// QueryWithPagination 分页查询
func QueryWithPagination(collectionName string, params map[string]interface{}) (*response.PagedResponse, error) {
	// 提取分页参数
	page := 1
	size := 10
	order := "-_id"
	
	if p, ok := params["page"].(int); ok && p > 0 {
		page = p
	}
	if s, ok := params["size"].(int); ok && s > 0 && s <= 1000 {
		size = s
	}
	if o, ok := params["order"].(string); ok && o != "" {
		order = o
	}
	
	// 构建查询条件
	filter := bson.M{}
	
	// 处理查询参数
	for key, value := range params {
		if key == "page" || key == "size" || key == "order" || value == nil {
			continue
		}
		
		// 处理ID字段
		if key == "id" || key == "_id" {
			if strValue, ok := value.(string); ok && strValue != "" {
				if objectID, err := primitive.ObjectIDFromHex(strValue); err == nil {
					filter["_id"] = objectID
				}
			}
			continue
		}
		
		// 处理字符串字段的模糊查询
		if strValue, ok := value.(string); ok && strValue != "" {
			if strings.HasSuffix(key, "__like") {
				realKey := strings.TrimSuffix(key, "__like")
				filter[realKey] = bson.M{"$regex": strValue, "$options": "i"}
			} else if strings.HasSuffix(key, "__eq") {
				realKey := strings.TrimSuffix(key, "__eq")
				filter[realKey] = strValue
			} else {
				// 默认使用模糊查询
				filter[key] = bson.M{"$regex": strValue, "$options": "i"}
			}
		} else {
			filter[key] = value
		}
	}
	
	// 构建排序
	sort := bson.D{}
	orderFields := strings.Split(order, ",")
	for _, field := range orderFields {
		field = strings.TrimSpace(field)
		direction := 1
		if strings.HasPrefix(field, "-") {
			direction = -1
			field = strings.TrimPrefix(field, "-")
		}
		sort = append(sort, bson.E{Key: field, Value: direction})
	}
	
	collection := GetCollection(collectionName)
	
	// 计算跳过的文档数
	skip := int64(size * (page - 1))
	limit := int64(size)
	
	// 执行查询
	findOptions := options.Find().
		SetSort(sort).
		SetSkip(skip).
		SetLimit(limit)
	
	cursor, err := collection.Find(context.Background(), filter, findOptions)
	if err != nil {
		return nil, fmt.Errorf("failed to execute query: %v", err)
	}
	defer cursor.Close(context.Background())
	
	// 解析结果
	var results []interface{}
	for cursor.Next(context.Background()) {
		var result bson.M
		if err := cursor.Decode(&result); err != nil {
			return nil, fmt.Errorf("failed to decode result: %v", err)
		}
		results = append(results, result)
	}
	
	if err := cursor.Err(); err != nil {
		return nil, fmt.Errorf("cursor error: %v", err)
	}
	
	// 获取总数
	total, err := collection.CountDocuments(context.Background(), filter)
	if err != nil {
		return nil, fmt.Errorf("failed to count documents: %v", err)
	}
	
	// 构建响应
	pageResponse := response.SuccessPage(results, page, size, total)
	return &pageResponse, nil
}
