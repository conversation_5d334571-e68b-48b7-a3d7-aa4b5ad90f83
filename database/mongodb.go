package database

import (
	"context"
	"demo/config"
	"demo/response"
	"fmt"
	"log"
	"reflect"
	"strconv"
	"strings"
	"time"

	"go.mongodb.org/mongo-driver/v2/bson"
	"go.mongodb.org/mongo-driver/v2/mongo"
	"go.mongodb.org/mongo-driver/v2/mongo/options"
)

var mongoClient *mongo.Client
var mongoDB *mongo.Database

// MongoDBAdapter MongoDB适配器
type MongoDBAdapter struct{}

// initMongoDB 初始化MongoDB数据库连接
func initMongoDB() error {
	cfg := config.AppConfig.MongoDB
	
	// 构建连接字符串
	uri := cfg.GetConnectionString()
	
	// 连接选项
	clientOptions := options.Client().ApplyURI(uri)
	clientOptions.SetMaxPoolSize(100)
	clientOptions.SetMinPoolSize(10)
	clientOptions.SetMaxConnIdleTime(30 * time.Second)
	clientOptions.SetConnectTimeout(30 * time.Second)
	clientOptions.SetServerSelectionTimeout(30 * time.Second)
	
	// 连接MongoDB
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	
	client, err := mongo.Connect(clientOptions)
	if err != nil {
		return fmt.Errorf("failed to connect to MongoDB: %v", err)
	}
	
	// 测试连接
	if err := client.Ping(ctx, nil); err != nil {
		return fmt.Errorf("failed to ping MongoDB: %v", err)
	}
	
	mongoClient = client
	mongoDB = client.Database(cfg.DBName)
	
	log.Printf("MongoDB connected successfully to %s:%d/%s", cfg.Host, cfg.Port, cfg.DBName)
	return nil
}

// Close 关闭数据库连接
func (m *MongoDBAdapter) Close() error {
	if mongoClient != nil {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		if err := mongoClient.Disconnect(ctx); err != nil {
			log.Printf("Error disconnecting from MongoDB: %v", err)
			return err
		}
		log.Println("MongoDB connection closed")
	}
	return nil
}

// GetCollection 获取集合
func GetCollection(name string) *mongo.Collection {
	return mongoDB.Collection(name)
}

// Create 创建文档
func (m *MongoDBAdapter) Create(collectionName string, entity interface{}) (interface{}, error) {
	collection := GetCollection(collectionName)
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	
	// 设置时间戳
	setTimestamps(entity, true)
	
	result, err := collection.InsertOne(ctx, entity)
	if err != nil {
		return nil, fmt.Errorf("failed to create document: %v", err)
	}
	
	return result.InsertedID, nil
}

// GetByID 根据ID获取文档
func (m *MongoDBAdapter) GetByID(collectionName string, id string, result interface{}) error {
	collection := GetCollection(collectionName)
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	
	objectID, err := bson.ObjectIDFromHex(id)
	if err != nil {
		return fmt.Errorf("invalid ID format: %v", err)
	}
	
	err = collection.FindOne(ctx, bson.M{"_id": objectID}).Decode(result)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return fmt.Errorf("entity not found")
		}
		return fmt.Errorf("failed to get document: %v", err)
	}
	
	return nil
}

// Update 更新文档
func (m *MongoDBAdapter) Update(collectionName string, id string, update interface{}) error {
	collection := GetCollection(collectionName)
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	
	objectID, err := bson.ObjectIDFromHex(id)
	if err != nil {
		return fmt.Errorf("invalid ID format: %v", err)
	}
	
	// 设置更新时间
	setTimestamps(update, false)
	
	result, err := collection.UpdateOne(
		ctx,
		bson.M{"_id": objectID},
		bson.M{"$set": update},
	)
	
	if err != nil {
		return fmt.Errorf("failed to update document: %v", err)
	}
	
	if result.MatchedCount == 0 {
		return fmt.Errorf("entity not found")
	}
	
	return nil
}

// Delete 删除文档
func (m *MongoDBAdapter) Delete(collectionName string, id string) error {
	collection := GetCollection(collectionName)
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	
	objectID, err := bson.ObjectIDFromHex(id)
	if err != nil {
		return fmt.Errorf("invalid ID format: %v", err)
	}
	
	result, err := collection.DeleteOne(ctx, bson.M{"_id": objectID})
	if err != nil {
		return fmt.Errorf("failed to delete document: %v", err)
	}
	
	if result.DeletedCount == 0 {
		return fmt.Errorf("entity not found")
	}
	
	return nil
}

// QueryWithPagination 分页查询
func (m *MongoDBAdapter) QueryWithPagination(collectionName string, params map[string]interface{}) (*response.PagedResponse, error) {
	collection := GetCollection(collectionName)
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	
	// 解析分页参数
	page := int64(1)
	size := int64(10)
	
	if p, ok := params["page"]; ok {
		if pInt, err := strconv.ParseInt(fmt.Sprintf("%v", p), 10, 64); err == nil && pInt > 0 {
			page = pInt
		}
	}
	if s, ok := params["size"]; ok {
		if sInt, err := strconv.ParseInt(fmt.Sprintf("%v", s), 10, 64); err == nil && sInt > 0 && sInt <= 1000 {
			size = sInt
		}
	}
	
	// 构建查询条件
	filter := buildFilter(params)
	
	// 计算总数
	total, err := collection.CountDocuments(ctx, filter)
	if err != nil {
		return nil, fmt.Errorf("failed to count documents: %v", err)
	}
	
	// 构建排序
	sort := buildSort(params)
	
	// 查询数据
	skip := (page - 1) * size
	findOptions := options.Find().SetSkip(skip).SetLimit(size).SetSort(sort)
	
	cursor, err := collection.Find(ctx, filter, findOptions)
	if err != nil {
		return nil, fmt.Errorf("failed to find documents: %v", err)
	}
	defer cursor.Close(ctx)
	
	var results []interface{}
	if err := cursor.All(ctx, &results); err != nil {
		return nil, fmt.Errorf("failed to decode documents: %v", err)
	}
	
	// 构建分页响应
	totalPages := (total + size - 1) / size
	
	return &response.PagedResponse{
		StandardResponse: response.StandardResponse{
			Code:      response.CodeSuccess,
			Message:   "success",
			Data:      results,
			Timestamp: time.Now().Unix(),
		},
		Pagination: response.PaginationInfo{
			Page:       int(page),
			Size:       int(size),
			Total:      total,
			TotalPages: int(totalPages),
			HasNext:    page < totalPages,
			HasPrev:    page > 1,
		},
	}, nil
}

// setTimestamps 设置时间戳
func setTimestamps(entity interface{}, isCreate bool) {
	v := reflect.ValueOf(entity)
	if v.Kind() == reflect.Ptr {
		v = v.Elem()
	}
	
	if v.Kind() == reflect.Struct {
		now := time.Now()
		
		if isCreate {
			if createdAtField := v.FieldByName("CreatedAt"); createdAtField.IsValid() && createdAtField.CanSet() {
				createdAtField.Set(reflect.ValueOf(now))
			}
		}
		
		if updatedAtField := v.FieldByName("UpdatedAt"); updatedAtField.IsValid() && updatedAtField.CanSet() {
			updatedAtField.Set(reflect.ValueOf(now))
		}
	}
}

// buildFilter 构建查询过滤器
func buildFilter(params map[string]interface{}) bson.M {
	filter := bson.M{}
	
	// 这里可以添加更多的查询条件解析逻辑
	// 例如：name__like, status__eq 等
	for key, value := range params {
		if strings.HasSuffix(key, "__eq") {
			fieldName := strings.TrimSuffix(key, "__eq")
			filter[fieldName] = value
		} else if strings.HasSuffix(key, "__like") {
			fieldName := strings.TrimSuffix(key, "__like")
			filter[fieldName] = bson.M{"$regex": value, "$options": "i"}
		}
		// 可以添加更多操作符：__gt, __lt, __in 等
	}
	
	return filter
}

// buildSort 构建排序
func buildSort(params map[string]interface{}) bson.D {
	sort := bson.D{}
	
	if orderStr, ok := params["order"].(string); ok && orderStr != "" {
		if strings.HasPrefix(orderStr, "-") {
			// 降序
			fieldName := strings.TrimPrefix(orderStr, "-")
			sort = append(sort, bson.E{Key: fieldName, Value: -1})
		} else {
			// 升序
			sort = append(sort, bson.E{Key: orderStr, Value: 1})
		}
	} else {
		// 默认按创建时间降序
		sort = append(sort, bson.E{Key: "created_at", Value: -1})
	}
	
	return sort
}
