package database

import (
	"demo/config"
	"demo/response"
	"fmt"
	"log"
)

// DatabaseInterface 数据库接口
type DatabaseInterface interface {
	Create(collectionName string, entity interface{}) (interface{}, error)
	GetByID(collectionName string, id string, result interface{}) error
	Update(collectionName string, id string, update interface{}) error
	Delete(collectionName string, id string) error
	QueryWithPagination(collectionName string, params map[string]interface{}) (*response.PagedResponse, error)
	Close() error
}

var currentDB DatabaseInterface

// InitDB 根据配置初始化数据库
func InitDB() error {
	dbType := config.AppConfig.Database.Type
	
	switch dbType {
	case "mongodb":
		log.Println("Initializing MongoDB database...")
		if err := initMongoDB(); err != nil {
			log.Printf("Failed to initialize MongoDB: %v", err)
			log.Println("Falling back to memory database...")
			return initMemoryDB()
		}
		currentDB = &MongoDBAdapter{}
		log.Println("MongoDB database initialized successfully")
		return nil
		
	case "memory":
		log.Println("Initializing memory database...")
		return initMemoryDB()
		
	default:
		log.Printf("Unknown database type: %s, falling back to memory database", dbType)
		return initMemoryDB()
	}
}

// initMemoryDB 初始化内存数据库
func initMemoryDB() error {
	memDB := NewMemoryDB()
	currentDB = memDB
	log.Println("Memory database initialized successfully")
	return nil
}

// CloseDB 关闭数据库连接
func CloseDB() error {
	if currentDB != nil {
		return currentDB.Close()
	}
	return nil
}

// Create 创建文档
func Create(collectionName string, entity interface{}) (interface{}, error) {
	if currentDB == nil {
		return nil, fmt.Errorf("database not initialized")
	}
	return currentDB.Create(collectionName, entity)
}

// GetByID 根据ID获取文档
func GetByID(collectionName string, id string, result interface{}) error {
	if currentDB == nil {
		return fmt.Errorf("database not initialized")
	}
	return currentDB.GetByID(collectionName, id, result)
}

// Update 更新文档
func Update(collectionName string, id string, update interface{}) error {
	if currentDB == nil {
		return fmt.Errorf("database not initialized")
	}
	return currentDB.Update(collectionName, id, update)
}

// Delete 删除文档
func Delete(collectionName string, id string) error {
	if currentDB == nil {
		return fmt.Errorf("database not initialized")
	}
	return currentDB.Delete(collectionName, id)
}

// QueryWithPagination 分页查询
func QueryWithPagination(collectionName string, params map[string]interface{}) (*response.PagedResponse, error) {
	if currentDB == nil {
		return nil, fmt.Errorf("database not initialized")
	}
	return currentDB.QueryWithPagination(collectionName, params)
}
