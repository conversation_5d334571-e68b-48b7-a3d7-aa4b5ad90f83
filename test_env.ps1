# Test .env file configuration

Write-Host "Testing .env file configuration..." -ForegroundColor Yellow

try {
    $response = Invoke-WebRequest -Uri "http://localhost:5019/health" -Method GET
    Write-Host "Health check success on port 5019: $($response.StatusCode)" -ForegroundColor Green
    Write-Host $response.Content
    Write-Host ".env file configuration is working!" -ForegroundColor Green
} catch {
    Write-Host "Failed to connect to port 5019: $($_.Exception.Message)" -ForegroundColor Red
    
    # Try the old port
    Write-Host "Trying old port 5018..." -ForegroundColor Yellow
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:5018/health" -Method GET
        Write-Host "Health check success on port 5018: $($response.StatusCode)" -ForegroundColor Yellow
        Write-Host ".env file may not be loaded correctly" -ForegroundColor Yellow
    } catch {
        Write-Host "Both ports failed" -ForegroundColor Red
    }
}
