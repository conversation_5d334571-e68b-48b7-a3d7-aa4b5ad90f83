# 🚀 Task API 使用指南

## 📊 **Task 功能模块完整示例**

基于 `model/Task.go` 创建的完整 CRUD 功能，展示了 API 模板的标准实现模式。

## 🔧 **模块结构**

### **✅ 文件组织**
```
demo/
├── model/
│   └── Task.go              # 任务模型
├── service/
│   └── task_service.go      # 任务业务逻辑服务
├── controller/
│   └── task_controller.go   # 任务控制器
└── utils/
    └── controller_utils.go  # 控制器工具函数
```

### **✅ 架构层次**
```
Controller Layer (HTTP 处理)
    ↓
Service Layer (业务逻辑)
    ↓
Repository Layer (数据访问)
    ↓
Database (MongoDB)
```

## 🎯 **API 接口列表**

### **✅ 1. 创建任务**
```http
POST /api/task
Content-Type: application/json

{
    "name": "完成项目文档",
    "content": "编写项目的API文档和使用说明"
}
```

**响应示例：**
```json
{
    "code": 200,
    "message": "创建任务成功",
    "data": {
        "id": "507f1f77bcf86cd799439011",
        "name": "完成项目文档",
        "content": "编写项目的API文档和使用说明",
        "created_at": "2024-01-01T10:00:00Z",
        "updated_at": "2024-01-01T10:00:00Z"
    }
}
```

### **✅ 2. 获取任务详情**
```http
GET /api/task/507f1f77bcf86cd799439011
```

**响应示例：**
```json
{
    "code": 200,
    "message": "获取任务详情成功",
    "data": {
        "id": "507f1f77bcf86cd799439011",
        "name": "完成项目文档",
        "content": "编写项目的API文档和使用说明",
        "created_at": "2024-01-01T10:00:00Z",
        "updated_at": "2024-01-01T10:00:00Z"
    }
}
```

### **✅ 3. 更新任务**
```http
PUT /api/task/507f1f77bcf86cd799439011
Content-Type: application/json

{
    "name": "完成项目文档（更新）",
    "content": "编写项目的API文档、使用说明和部署指南"
}
```

**响应示例：**
```json
{
    "code": 200,
    "message": "更新任务成功",
    "data": {
        "id": "507f1f77bcf86cd799439011",
        "name": "完成项目文档（更新）",
        "content": "编写项目的API文档、使用说明和部署指南",
        "created_at": "2024-01-01T10:00:00Z",
        "updated_at": "2024-01-01T10:30:00Z"
    }
}
```

### **✅ 4. 删除任务**
```http
DELETE /api/task/507f1f77bcf86cd799439011
```

**响应示例：**
```json
{
    "code": 200,
    "message": "删除任务成功",
    "data": null
}
```

### **✅ 5. 查询任务列表**
```http
GET /api/task/query?name=文档&page=1&size=10
```

**查询参数：**
- `name`: 任务名称（模糊搜索）
- `content`: 任务内容（模糊搜索）
- `page`: 页码（默认1）
- `size`: 每页大小（默认10，最大100）

**响应示例：**
```json
{
    "code": 200,
    "message": "查询任务成功",
    "data": {
        "data": [
            {
                "id": "507f1f77bcf86cd799439011",
                "name": "完成项目文档",
                "content": "编写项目的API文档和使用说明",
                "created_at": "2024-01-01T10:00:00Z",
                "updated_at": "2024-01-01T10:00:00Z"
            }
        ],
        "total": 1,
        "page": 1,
        "size": 10,
        "pages": 1
    }
}
```

## 🎁 **代码特点**

### **✅ 1. 简洁的模型设计**
```go
// model/Task.go
type Task struct {
    base.ModelBase `bson:",inline"`
    Name           string `json:"name,omitempty" bson:"name,omitempty"`
    Content        string `json:"content,omitempty" bson:"content,omitempty"`
}
```

### **✅ 2. 清晰的业务逻辑**
```go
// service/task_service.go
func (ts *TaskService) CreateTask(ctx context.Context, req *CreateTaskRequest) (*model.Task, error) {
    // 1. 业务验证
    // 2. 业务规则检查
    // 3. 构建实体
    // 4. 调用数据层
    // 5. 返回结果
}
```

### **✅ 3. 统一的控制器模式**
```go
// controller/task_controller.go
func (tc *TaskController) CreateTask(c *gin.Context) {
    // 1. 参数绑定和验证
    // 2. 调用 service 层
    // 3. 处理响应
}
```

### **✅ 4. 自动路由注册**
```go
func init() {
    router.Api.POST("/task", taskController.CreateTask)
    router.Api.GET("/task/:id", taskController.GetTaskDetail)
    router.Api.PUT("/task/:id", taskController.UpdateTask)
    router.Api.DELETE("/task/:id", taskController.DeleteTask)
    router.Api.GET("/task/query", taskController.QueryTasks)
}
```

## 🚀 **业务逻辑特性**

### **✅ 1. 数据验证**
- 任务名称不能为空，最大100字符
- 任务内容最大1000字符
- 任务名称唯一性检查

### **✅ 2. 错误处理**
- 统一的错误响应格式
- 详细的错误信息
- 适当的 HTTP 状态码

### **✅ 3. 分页查询**
- 支持模糊搜索
- 分页参数验证
- 统一的分页响应格式

### **✅ 4. 参数处理**
- 自动参数绑定
- 参数验证
- 类型转换

## 🎯 **使用 curl 测试**

### **创建任务**
```bash
curl -X POST http://localhost:8080/api/task \
  -H "Content-Type: application/json" \
  -d '{
    "name": "学习Go语言",
    "content": "完成Go语言基础教程的学习"
  }'
```

### **查询任务列表**
```bash
curl "http://localhost:8080/api/task/query?name=Go&page=1&size=5"
```

### **获取任务详情**
```bash
curl http://localhost:8080/api/task/507f1f77bcf86cd799439011
```

### **更新任务**
```bash
curl -X PUT http://localhost:8080/api/task/507f1f77bcf86cd799439011 \
  -H "Content-Type: application/json" \
  -d '{
    "name": "深入学习Go语言",
    "content": "完成Go语言高级特性的学习和实践"
  }'
```

### **删除任务**
```bash
curl -X DELETE http://localhost:8080/api/task/507f1f77bcf86cd799439011
```

## 🏆 **模板价值**

这个 Task 功能模块展示了：

1. **✅ 标准的 CRUD 操作实现**
2. **✅ 清晰的分层架构**
3. **✅ 统一的错误处理**
4. **✅ 完整的参数验证**
5. **✅ 自动路由注册**
6. **✅ 分页查询支持**

开发者可以参考这个模块快速创建其他功能模块！
