// MongoDB初始化脚本
// 创建应用数据库和用户

// 切换到admin数据库
db = db.getSiblingDB('admin');

// 创建应用用户（可选，如果需要认证）
// db.createUser({
//   user: 'demouser',
//   pwd: 'demopassword',
//   roles: [
//     {
//       role: 'readWrite',
//       db: 'demoDb'
//     }
//   ]
// });

// 切换到应用数据库
db = db.getSiblingDB('demoDb');

// 创建任务集合并插入示例数据
db.tasks.insertMany([
  {
    name: "示例任务1",
    content: "这是一个示例任务内容",
    status: "pending",
    created_at: new Date(),
    updated_at: new Date()
  },
  {
    name: "示例任务2", 
    content: "这是另一个示例任务内容",
    status: "completed",
    created_at: new Date(),
    updated_at: new Date()
  }
]);

// 创建索引
db.tasks.createIndex({ "name": 1 });
db.tasks.createIndex({ "status": 1 });
db.tasks.createIndex({ "created_at": -1 });

print("MongoDB初始化完成！");
print("- 数据库: demoDb");
print("- 集合: tasks");
print("- 示例数据已插入");
print("- 索引已创建");
