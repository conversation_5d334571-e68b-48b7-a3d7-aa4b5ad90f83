package router

import (
	"reflect"
	"strings"

	"github.com/gin-gonic/gin"
)

// RouteInfo 路由信息
type RouteInfo struct {
	Method  string
	Path    string
	Handler gin.HandlerFunc
}

// Controller 控制器接口
type Controller interface{}

// RouteRegistry 路由注册器
type RouteRegistry struct {
	routes []RouteInfo
}

var registry = &RouteRegistry{
	routes: make([]RouteInfo, 0),
}

// RegisterController 注册控制器 - 使用反射自动发现路由
func RegisterController(controller Controller) {
	controllerType := reflect.TypeOf(controller)
	controllerValue := reflect.ValueOf(controller)

	// 遍历控制器的所有方法
	for i := 0; i < controllerType.NumMethod(); i++ {
		method := controllerType.Method(i)
		methodValue := controllerValue.Method(i)

		// 检查方法是否符合 gin.HandlerFunc 签名
		if isGinHandler(method.Type) {
			// 从方法名推断路由信息
			routeInfo := inferRouteFromMethod(method.Name, methodValue.Interface().(gin.HandlerFunc))
			if routeInfo != nil {
				registry.routes = append(registry.routes, *routeInfo)
			}
		}
	}
}

// isGinHandler 检查是否为 gin.HandlerFunc
func isGinHandler(methodType reflect.Type) bool {
	// gin.HandlerFunc 的签名: func(*gin.Context)
	if methodType.NumIn() != 2 || methodType.NumOut() != 0 {
		return false
	}
	
	// 检查参数类型
	paramType := methodType.In(1)
	return paramType.String() == "*gin.Context"
}

// inferRouteFromMethod 从方法名推断路由信息
func inferRouteFromMethod(methodName string, handler gin.HandlerFunc) *RouteInfo {
	// 简单的命名约定映射
	routeMap := map[string]RouteInfo{
		"GetTaskList":  {Method: "GET", Path: "/task/query"},
		"CreateTask":   {Method: "POST", Path: "/task/create"},
		"UpdateTask":   {Method: "PUT", Path: "/task/update"},
		"DeleteTask":   {Method: "DELETE", Path: "/task/delete"},
		"GetTask":      {Method: "GET", Path: "/task/get"},
		// 可以添加更多映射规则
	}

	if route, exists := routeMap[methodName]; exists {
		route.Handler = handler
		return &route
	}

	return nil
}

// ApplyRoutes 应用所有注册的路由
func ApplyRoutes(router *gin.RouterGroup) {
	for _, route := range registry.routes {
		switch route.Method {
		case "GET":
			router.GET(route.Path, route.Handler)
		case "POST":
			router.POST(route.Path, route.Handler)
		case "PUT":
			router.PUT(route.Path, route.Handler)
		case "DELETE":
			router.DELETE(route.Path, route.Handler)
		case "PATCH":
			router.PATCH(route.Path, route.Handler)
		}
	}
}

// 更高级的注解风格实现（使用结构体标签）

// RestController 带注解的控制器基类
type RestController struct {
	BasePath string `route:"/api/v1"`
}

// RouteMapping 路由映射注解
type RouteMapping struct {
	Method string `json:"method"`
	Path   string `json:"path"`
}

// GetMapping GET请求映射
func GetMapping(path string) RouteMapping {
	return RouteMapping{Method: "GET", Path: path}
}

// PostMapping POST请求映射
func PostMapping(path string) RouteMapping {
	return RouteMapping{Method: "POST", Path: path}
}

// PutMapping PUT请求映射
func PutMapping(path string) RouteMapping {
	return RouteMapping{Method: "PUT", Path: path}
}

// DeleteMapping DELETE请求映射
func DeleteMapping(path string) RouteMapping {
	return RouteMapping{Method: "DELETE", Path: path}
}

// AnnotatedTaskController 使用注解风格的任务控制器示例
type AnnotatedTaskController struct {
	RestController `route:"/api/v1/tasks"`
}

// 方法级别的路由定义（通过注释约定）
/*
@GetMapping("/query")
@RequestParam: taskQueryParam
*/
func (tc *AnnotatedTaskController) GetTaskList(c *gin.Context) {
	// 实现逻辑...
}

/*
@PostMapping("/create")
@RequestBody: taskCreateParam
*/
func (tc *AnnotatedTaskController) CreateTask(c *gin.Context) {
	// 实现逻辑...
}

/*
@PutMapping("/update")
@RequestBody: taskUpdateParam
*/
func (tc *AnnotatedTaskController) UpdateTask(c *gin.Context) {
	// 实现逻辑...
}

/*
@DeleteMapping("/delete")
@RequestBody: taskDeleteParam
*/
func (tc *AnnotatedTaskController) DeleteTask(c *gin.Context) {
	// 实现逻辑...
}

/*
@GetMapping("/get")
@RequestParam: taskGetParam
*/
func (tc *AnnotatedTaskController) GetTask(c *gin.Context) {
	// 实现逻辑...
}

// AutoRegisterRoutes 自动注册路由（基于注释解析）
func AutoRegisterRoutes(router *gin.RouterGroup, controllers ...Controller) {
	for _, controller := range controllers {
		RegisterController(controller)
	}
	ApplyRoutes(router)
}
