package middleware

import (
	"context"
	"demo/response"
	"fmt"
	"log"
	"net/http"
	"runtime/debug"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// RequestID 中间件 - 为每个请求生成唯一ID
func RequestID() gin.HandlerFunc {
	return func(c *gin.Context) {
		requestID := c.<PERSON>eader("X-Request-ID")
		if requestID == "" {
			requestID = uuid.New().String()
		}
		
		c.<PERSON><PERSON>("X-Request-ID", requestID)
		c.Set("request_id", requestID)
		c.Next()
	}
}

// Logger 中间件 - 请求日志记录
func Logger() gin.HandlerFunc {
	return gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		return fmt.Sprintf("[%s] %s %s %s %d %s %s\n",
			param.TimeStamp.Format("2006-01-02 15:04:05"),
			param.Method,
			param.Path,
			param.ClientIP,
			param.StatusCode,
			param.Latency,
			param.ErrorMessage,
		)
	})
}

// Recovery 中间件 - 恢复panic
func Recovery() gin.HandlerFunc {
	return gin.CustomRecovery(func(c *gin.Context, recovered interface{}) {
		requestID, _ := c.Get("request_id")
		
		log.Printf("Panic recovered: %v\nRequest ID: %v\nStack: %s", 
			recovered, requestID, debug.Stack())
		
		resp := response.NewResponseBuilder().
			Error(response.CodeInternalError, "Internal server error").
			WithRequestID(fmt.Sprintf("%v", requestID)).
			Build()
		
		c.JSON(http.StatusInternalServerError, resp)
		c.Abort()
	})
}

// Security 中间件 - 基本安全头设置
func Security() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("X-Content-Type-Options", "nosniff")
		c.Header("X-Frame-Options", "DENY")
		c.Header("X-XSS-Protection", "1; mode=block")
		c.Header("Referrer-Policy", "strict-origin-when-cross-origin")
		c.Next()
	}
}

// ValidateJSON 中间件 - JSON格式验证
func ValidateJSON() gin.HandlerFunc {
	return func(c *gin.Context) {
		if c.Request.Method == "POST" || c.Request.Method == "PUT" || c.Request.Method == "PATCH" {
			contentType := c.GetHeader("Content-Type")
			if contentType != "" && !strings.Contains(contentType, "application/json") {
				resp := response.Fail("Content-Type must be application/json")
				c.JSON(http.StatusBadRequest, resp)
				c.Abort()
				return
			}
		}
		c.Next()
	}
}

// RateLimiter 简单的速率限制中间件
func RateLimiter(maxRequests int, window time.Duration) gin.HandlerFunc {
	type client struct {
		requests int
		lastSeen time.Time
	}
	
	clients := make(map[string]*client)
	
	return func(c *gin.Context) {
		ip := c.ClientIP()
		now := time.Now()
		
		if cl, exists := clients[ip]; exists {
			if now.Sub(cl.lastSeen) > window {
				cl.requests = 1
				cl.lastSeen = now
			} else {
				cl.requests++
				if cl.requests > maxRequests {
					resp := response.Fail("Rate limit exceeded")
					c.JSON(http.StatusTooManyRequests, resp)
					c.Abort()
					return
				}
			}
		} else {
			clients[ip] = &client{
				requests: 1,
				lastSeen: now,
			}
		}
		
		c.Next()
	}
}

// Timeout 中间件 - 请求超时控制
func Timeout(timeout time.Duration) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 设置超时上下文
		ctx, cancel := context.WithTimeout(c.Request.Context(), timeout)
		defer cancel()
		
		c.Request = c.Request.WithContext(ctx)
		
		// 使用channel来检测是否超时
		finished := make(chan struct{})
		go func() {
			c.Next()
			finished <- struct{}{}
		}()
		
		select {
		case <-finished:
			// 请求正常完成
		case <-ctx.Done():
			// 请求超时
			resp := response.Fail("Request timeout")
			c.JSON(http.StatusRequestTimeout, resp)
			c.Abort()
		}
	}
}
