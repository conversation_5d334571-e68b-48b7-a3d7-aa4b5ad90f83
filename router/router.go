package router

import (
	"demo/config"
	"demo/middleware"
	"net/http"
	"time"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
)

var router *gin.Engine
var Api *gin.RouterGroup

// SetupCORS 统一的跨域配置
func SetupCORS() gin.HandlerFunc {
	config := cors.DefaultConfig()
	config.AllowMethods = []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"}
	config.AllowHeaders = []string{"Origin", "Content-Type", "Token", "Authorization", "X-Request-ID"}
	config.AllowOrigins = []string{
		"http://localhost:3000",
		"http://localhost:8080",
		"http://localhost:8081",
		"http://localhost:5173",
		"http://localhost:5175",
		"http://*************:8081",
		"http://*************:5174",
		// 可以从配置文件读取
	}
	config.AllowCredentials = true
	return cors.New(config)
}

func init() {
	// 设置Gin模式
	if config.AppConfig != nil {
		gin.SetMode(config.AppConfig.Server.Mode)
	}

	router = gin.New()

	// 添加基础中间件
	router.Use(middleware.RequestID())
	router.Use(middleware.Logger())
	router.Use(middleware.Recovery())
	router.Use(Cors())

	// 健康检查端点
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":    "ok",
			"timestamp": time.Now().Unix(),
		})
	})

	// API路由组 - 保持你原来的设计
	Api = router.Group("/api/v1")
}

func InitRouter() *gin.Engine {
	return router
}
