package router

import (
	"demo/config"
	"demo/router/middleware"
	"net/http"
	"time"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
)

var router *gin.Engine
var Api *gin.RouterGroup

// SetupCORS 统一的跨域配置
func SetupCORS() gin.HandlerFunc {
	config := cors.DefaultConfig()
	config.AllowMethods = []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"}
	config.AllowHeaders = []string{"Origin", "Content-Type", "Token", "Authorization", "X-Request-ID"}
	config.AllowOrigins = []string{
		"http://localhost:3000",
		"http://localhost:8080",
		"http://localhost:8081",
		"http://localhost:5173",
		"http://localhost:5175",
		"http://*************:8081",
		"http://*************:5174",
		// 可以从配置文件读取
	}
	config.AllowCredentials = true
	return cors.New(config)
}

func init() {
	// 设置Gin模式
	if config.AppConfig != nil {
		gin.SetMode(config.AppConfig.Server.Mode)
	}

	router = gin.New()

	// 统一设置所有中间件
	setupMiddleware()

	// 设置基础路由
	setupBasicRoutes()

	// API路由组 - 保持你原来的设计
	Api = router.Group("/api/v1")
}

// setupMiddleware 统一的中间件配置
func setupMiddleware() {
	// 基础中间件
	router.Use(middleware.RequestID())
	router.Use(middleware.Logger())
	router.Use(middleware.Recovery())
	router.Use(middleware.Security())

	// CORS中间件
	router.Use(SetupCORS())

	// JSON验证中间件
	router.Use(middleware.ValidateJSON())

	// 可选中间件（生产环境可启用）
	// if gin.Mode() == gin.ReleaseMode {
	//     router.Use(middleware.RateLimiter(100, time.Minute))
	//     router.Use(middleware.Timeout(30 * time.Second))
	// }
}

// setupBasicRoutes 设置基础路由
func setupBasicRoutes() {
	// 健康检查端点
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":    "ok",
			"timestamp": time.Now().Unix(),
		})
	})

	// 404处理
	router.NoRoute(func(c *gin.Context) {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "Route not found",
		})
	})
}

func InitRouter() *gin.Engine {
	return router
}
