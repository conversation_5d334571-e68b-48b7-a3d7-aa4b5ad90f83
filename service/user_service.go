package service

import (
	"context"
	"demo/database"
	"demo/model"
	"demo/response"
	"fmt"
	"regexp"
	"time"

	"go.mongodb.org/mongo-driver/v2/bson"
	"golang.org/x/crypto/bcrypt"
)

// UserService 用户业务逻辑服务
type UserService struct {
	repository         *database.Repository[model.User]
	paginationService  *PaginationService[model.User]
}

// NewUserService 创建用户服务
func NewUserService() *UserService {
	return &UserService{
		repository:        database.NewRepository[model.User]("user"),
		paginationService: NewPaginationService[model.User]("user"),
	}
}

// CreateUser 创建用户 - 业务逻辑层
func (us *UserService) CreateUser(ctx context.Context, req *CreateUserRequest) (*model.User, error) {
	// 1. 业务验证：检查邮箱格式
	if !us.isValidEmail(req.Email) {
		return nil, fmt.Errorf("邮箱格式不正确")
	}

	// 2. 业务验证：检查密码强度
	if !us.isValidPassword(req.Password) {
		return nil, fmt.Errorf("密码必须至少8位，包含字母和数字")
	}

	// 3. 业务规则：检查邮箱是否已存在
	exists, err := us.isEmailExists(ctx, req.Email)
	if err != nil {
		return nil, fmt.Errorf("检查邮箱失败: %v", err)
	}
	if exists {
		return nil, fmt.Errorf("邮箱已存在")
	}

	// 4. 业务逻辑：密码加密
	hashedPassword, err := us.hashPassword(req.Password)
	if err != nil {
		return nil, fmt.Errorf("密码加密失败: %v", err)
	}

	// 5. 业务逻辑：构建用户实体
	user := &model.User{
		Username: req.Username,
		Email:    req.Email,
		Password: hashedPassword,
		Status:   "active",
	}
	// 设置时间戳
	user.DefaultCreatedAt()
	user.DefaultUpdatedAt()

	// 6. 调用数据层
	createdUser, err := us.repository.Create(ctx, user)
	if err != nil {
		return nil, fmt.Errorf("创建用户失败: %v", err)
	}

	// 7. 业务逻辑：清除敏感信息
	us.clearSensitiveInfo(createdUser)

	return createdUser, nil
}

// GetUserByID 根据ID获取用户 - 业务逻辑层
func (us *UserService) GetUserByID(ctx context.Context, userID string) (*model.User, error) {
	// 1. 业务验证：ID格式检查
	if userID == "" {
		return nil, fmt.Errorf("用户ID不能为空")
	}

	// 2. 调用数据层
	user, err := us.repository.FindOne(ctx, bson.M{"_id": userID})
	if err != nil {
		return nil, fmt.Errorf("用户不存在: %v", err)
	}

	// 3. 业务逻辑：清除敏感信息
	us.clearSensitiveInfo(user)

	return user, nil
}

// UpdateUser 更新用户 - 业务逻辑层
func (us *UserService) UpdateUser(ctx context.Context, userID string, req *UpdateUserRequest) (*model.User, error) {
	// 1. 业务验证：检查用户是否存在
	existingUser, err := us.repository.FindOne(ctx, bson.M{"_id": userID})
	if err != nil {
		return nil, fmt.Errorf("用户不存在: %v", err)
	}

	// 2. 业务验证：如果更新邮箱，检查新邮箱是否已被其他用户使用
	if req.Email != "" && req.Email != existingUser.Email {
		if !us.isValidEmail(req.Email) {
			return nil, fmt.Errorf("邮箱格式不正确")
		}
		
		exists, err := us.isEmailExistsExcludeUser(ctx, req.Email, userID)
		if err != nil {
			return nil, fmt.Errorf("检查邮箱失败: %v", err)
		}
		if exists {
			return nil, fmt.Errorf("邮箱已被其他用户使用")
		}
	}

	// 3. 业务逻辑：构建更新数据
	updateData := bson.M{
		"updated_at": time.Now(),
	}
	
	if req.Username != "" {
		updateData["username"] = req.Username
	}
	if req.Email != "" {
		updateData["email"] = req.Email
	}
	if req.Status != "" {
		updateData["status"] = req.Status
	}

	// 4. 调用数据层
	_, err = us.repository.UpdateByFilter(ctx, bson.M{"_id": userID}, updateData)
	if err != nil {
		return nil, fmt.Errorf("更新用户失败: %v", err)
	}

	// 5. 返回更新后的用户
	return us.GetUserByID(ctx, userID)
}

// GetUserList 获取用户列表 - 业务逻辑层
func (us *UserService) GetUserList(ctx context.Context, params map[string]interface{}) (*response.PageResponse, error) {
	// 1. 业务逻辑：处理查询参数
	processedParams := us.processQueryParams(params)

	// 2. 调用分页服务
	result, err := us.paginationService.QueryWithPagination(ctx, processedParams)
	if err != nil {
		return nil, fmt.Errorf("查询用户列表失败: %v", err)
	}

	// 3. 业务逻辑：清除列表中所有用户的敏感信息
	if users, ok := result.Data.([]model.User); ok {
		for i := range users {
			us.clearSensitiveInfo(&users[i])
		}
		result.Data = users
	}

	return result, nil
}

// ChangePassword 修改密码 - 业务逻辑层
func (us *UserService) ChangePassword(ctx context.Context, userID string, req *ChangePasswordRequest) error {
	// 1. 业务验证：获取用户
	user, err := us.repository.FindOne(ctx, bson.M{"_id": userID})
	if err != nil {
		return fmt.Errorf("用户不存在: %v", err)
	}

	// 2. 业务验证：验证旧密码
	if !us.checkPassword(req.OldPassword, user.Password) {
		return fmt.Errorf("旧密码不正确")
	}

	// 3. 业务验证：新密码强度检查
	if !us.isValidPassword(req.NewPassword) {
		return fmt.Errorf("新密码必须至少8位，包含字母和数字")
	}

	// 4. 业务逻辑：加密新密码
	hashedPassword, err := us.hashPassword(req.NewPassword)
	if err != nil {
		return fmt.Errorf("密码加密失败: %v", err)
	}

	// 5. 调用数据层更新密码
	_, err = us.repository.UpdateByFilter(ctx, bson.M{"_id": userID}, bson.M{
		"password":   hashedPassword,
		"updated_at": time.Now(),
	})
	if err != nil {
		return fmt.Errorf("更新密码失败: %v", err)
	}

	return nil
}

// ==================== 私有业务逻辑方法 ====================

// isValidEmail 验证邮箱格式 - 业务逻辑
func (us *UserService) isValidEmail(email string) bool {
	emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	return emailRegex.MatchString(email)
}

// isValidPassword 验证密码强度 - 业务逻辑
func (us *UserService) isValidPassword(password string) bool {
	if len(password) < 8 {
		return false
	}
	hasLetter := regexp.MustCompile(`[a-zA-Z]`).MatchString(password)
	hasNumber := regexp.MustCompile(`[0-9]`).MatchString(password)
	return hasLetter && hasNumber
}

// isEmailExists 检查邮箱是否存在 - 业务逻辑
func (us *UserService) isEmailExists(ctx context.Context, email string) (bool, error) {
	count, err := us.repository.Count(ctx, bson.M{"email": email})
	return count > 0, err
}

// isEmailExistsExcludeUser 检查邮箱是否被其他用户使用 - 业务逻辑
func (us *UserService) isEmailExistsExcludeUser(ctx context.Context, email, excludeUserID string) (bool, error) {
	count, err := us.repository.Count(ctx, bson.M{
		"email": email,
		"_id":   bson.M{"$ne": excludeUserID},
	})
	return count > 0, err
}

// hashPassword 密码加密 - 业务逻辑
func (us *UserService) hashPassword(password string) (string, error) {
	bytes, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	return string(bytes), err
}

// checkPassword 验证密码 - 业务逻辑
func (us *UserService) checkPassword(password, hash string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(hash), []byte(password))
	return err == nil
}

// clearSensitiveInfo 清除敏感信息 - 业务逻辑
func (us *UserService) clearSensitiveInfo(user *model.User) {
	user.Password = "" // 不返回密码
}

// processQueryParams 处理查询参数 - 业务逻辑
func (us *UserService) processQueryParams(params map[string]interface{}) map[string]interface{} {
	processed := make(map[string]interface{})
	
	for key, value := range params {
		switch key {
		case "username":
			// 用户名支持模糊搜索
			if str, ok := value.(string); ok && str != "" {
				processed["username__like"] = str
			}
		case "email":
			// 邮箱支持模糊搜索
			if str, ok := value.(string); ok && str != "" {
				processed["email__like"] = str
			}
		case "status":
			// 状态精确匹配
			if str, ok := value.(string); ok && str != "" {
				processed["status__eq"] = str
			}
		default:
			// 其他参数直接传递
			processed[key] = value
		}
	}
	
	return processed
}

// ==================== 请求结构体 ====================

type CreateUserRequest struct {
	Username string `json:"username" binding:"required" validate:"min=3,max=50"`
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required" validate:"min=8"`
}

type UpdateUserRequest struct {
	Username string `json:"username" validate:"min=3,max=50"`
	Email    string `json:"email" validate:"email"`
	Status   string `json:"status" validate:"oneof=active inactive"`
}

type ChangePasswordRequest struct {
	OldPassword string `json:"old_password" binding:"required"`
	NewPassword string `json:"new_password" binding:"required" validate:"min=8"`
}
