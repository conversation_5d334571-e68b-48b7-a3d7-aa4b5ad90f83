package service

import (
	"context"
	"demo/database"
	"demo/response"
	"fmt"
	"strings"

	"github.com/chenmingyong0423/go-mongox/v2/bsonx"
)

// PaginationParams 分页参数
type PaginationParams struct {
	Page  int    `form:"page" json:"page"`
	Size  int    `form:"size" json:"size"`
	Order string `form:"order" json:"order"`
}

// Validate 验证分页参数
func (p *PaginationParams) Validate() {
	if p.Page <= 0 {
		p.Page = 1
	}
	if p.<PERSON>ze <= 0 {
		p.Size = 10
	}
	if p.Size > 1000 { // 限制最大页面大小
		p.Size = 1000
	}
	if p.Order == "" {
		p.Order = "-_id"
	}
}

// GetSkip 获取跳过的记录数
func (p *PaginationParams) GetSkip() int64 {
	return int64(p.Size * (p.Page - 1))
}

// GetLimit 获取限制的记录数
func (p *PaginationParams) GetLimit() int64 {
	return int64(p.<PERSON><PERSON>)
}

// BuildSortOptions 构建排序选项
func (p *PaginationParams) BuildSortOptions() *bsonx.D {
	sortBsonx := bsonx.NewD()
	orderFields := strings.Split(p.Order, ",")

	for _, field := range orderFields {
		field = strings.TrimSpace(field)
		direction := 1

		if strings.HasPrefix(field, "-") {
			direction = -1
			field = strings.TrimPrefix(field, "-")
		} else if strings.HasPrefix(field, "+") {
			field = strings.TrimPrefix(field, "+")
		}

		sortBsonx = sortBsonx.Add(field, direction)
	}

	return sortBsonx
}

// PaginationService 分页服务 - 处理分页业务逻辑
type PaginationService[T any] struct {
	collectionName string
}

// NewPaginationService 创建分页服务
func NewPaginationService[T any](collectionName string) *PaginationService[T] {
	return &PaginationService[T]{
		collectionName: collectionName,
	}
}

// QueryWithPagination 带分页的查询 - 业务逻辑层
func (ps *PaginationService[T]) QueryWithPagination(
	ctx context.Context,
	params map[string]interface{},
) (*response.PageResponse, error) {
	// 1. 提取和验证分页参数
	pagination := ps.extractPaginationParams(params)
	pagination.Validate()

	// 2. 构建查询条件
	queryBuilder := database.NewQueryBuilder()
	query := queryBuilder.BuildFromMap(params)

	// 检查查询构建错误
	if queryBuilder.HasErrors() {
		return nil, fmt.Errorf("query build errors: %v", queryBuilder.GetErrors())
	}

	// 3. 调用数据层进行查询
	repository := database.NewRepository[T](ps.collectionName)
	results, total, err := repository.FindWithPagination(
		ctx,
		query.Build(),
		pagination.GetSkip(),
		pagination.GetLimit(),
		pagination.BuildSortOptions(),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to execute pagination query: %v", err)
	}

	// 4. 构建响应
	pageResponse := response.SuccessPage(results, pagination.Page, pagination.Size, total)
	return &pageResponse, nil
}

// QueryAll 查询所有记录（不分页）- 业务逻辑层
func (ps *PaginationService[T]) QueryAll(
	ctx context.Context,
	params map[string]interface{},
) ([]T, error) {
	// 1. 构建查询条件
	queryBuilder := database.NewQueryBuilder()
	query := queryBuilder.BuildFromMap(params)

	// 检查查询构建错误
	if queryBuilder.HasErrors() {
		return nil, fmt.Errorf("query build errors: %v", queryBuilder.GetErrors())
	}

	// 2. 调用数据层查询所有记录
	repository := database.NewRepository[T](ps.collectionName)
	results, err := repository.FindAll(ctx, query.Build())
	if err != nil {
		return nil, fmt.Errorf("failed to execute query all: %v", err)
	}

	return results, nil
}

// QueryOne 查询单个记录 - 业务逻辑层
func (ps *PaginationService[T]) QueryOne(
	ctx context.Context,
	params map[string]interface{},
) (*T, error) {
	// 1. 构建查询条件
	queryBuilder := database.NewQueryBuilder()
	query := queryBuilder.BuildFromMap(params)

	// 检查查询构建错误
	if queryBuilder.HasErrors() {
		return nil, fmt.Errorf("query build errors: %v", queryBuilder.GetErrors())
	}

	// 2. 调用数据层查询单个记录
	repository := database.NewRepository[T](ps.collectionName)
	result, err := repository.FindOne(ctx, query.Build())
	if err != nil {
		return nil, fmt.Errorf("failed to execute query one: %v", err)
	}

	return result, nil
}

// extractPaginationParams 提取分页参数
func (ps *PaginationService[T]) extractPaginationParams(params map[string]interface{}) *PaginationParams {
	pagination := &PaginationParams{
		Page:  1,
		Size:  10,
		Order: "-_id",
	}

	if page, ok := params["page"].(int); ok {
		pagination.Page = page
	}
	if size, ok := params["size"].(int); ok {
		pagination.Size = size
	}
	if order, ok := params["order"].(string); ok {
		pagination.Order = order
	}

	return pagination
}
