package service

import (
	"context"
	"demo/database"
	"demo/response"
	"fmt"
	"reflect"
	"strings"
	"time"

	"go.mongodb.org/mongo-driver/v2/bson"
)

// BaseService 基础服务接口
type BaseService[T any] interface {
	Create(ctx context.Context, entity *T) (*T, error)
	GetByID(ctx context.Context, id string) (*T, error)
	Update(ctx context.Context, id string, entity *T) (*T, error)
	Delete(ctx context.Context, id string) error
	List(ctx context.Context, params map[string]interface{}) (*response.PagedResponse, error)
	ListAll(ctx context.Context, params map[string]interface{}) ([]T, error)
}

// BaseServiceImpl 基础服务实现
type BaseServiceImpl[T any] struct {
	collectionName string
	pagination     *database.PaginationService[T]
}

// NewBaseService 创建基础服务
func NewBaseService[T any](collectionName string) BaseService[T] {
	return &BaseServiceImpl[T]{
		collectionName: collectionName,
		pagination:     database.NewPaginationService[T](collectionName),
	}
}

// Create 创建实体
func (s *BaseServiceImpl[T]) Create(ctx context.Context, entity *T) (*T, error) {
	collection := database.GetCollection[T](s.collectionName)

	// 设置创建时间等基础字段
	s.setCreateFields(entity)

	result, err := collection.Creator().InsertOne(ctx, entity)
	if err != nil {
		return nil, fmt.Errorf("failed to create entity: %v", err)
	}

	// 返回创建的实体（包含生成的ID）
	if result != nil {
		return result, nil
	}

	return entity, nil
}

// GetByID 根据ID获取实体
func (s *BaseServiceImpl[T]) GetByID(ctx context.Context, id string) (*T, error) {
	objectID, err := bson.ObjectIDFromHex(id)
	if err != nil {
		return nil, fmt.Errorf("invalid ID format: %v", err)
	}

	collection := database.GetCollection[T](s.collectionName)
	result, err := collection.Finder().Filter(map[string]interface{}{
		"_id": objectID,
	}).FindOne(ctx)

	if err != nil {
		return nil, fmt.Errorf("entity not found: %v", err)
	}

	return result, nil
}

// Update 更新实体
func (s *BaseServiceImpl[T]) Update(ctx context.Context, id string, entity *T) (*T, error) {
	objectID, err := bson.ObjectIDFromHex(id)
	if err != nil {
		return nil, fmt.Errorf("invalid ID format: %v", err)
	}

	// 设置更新时间等基础字段
	s.setUpdateFields(entity)

	collection := database.GetCollection[T](s.collectionName)

	// 使用反射构建更新文档
	updateDoc := s.buildUpdateDocument(entity)

	result, err := collection.Updater().Filter(map[string]interface{}{
		"_id": objectID,
	}).Updates(updateDoc).UpdateOne(ctx)

	if err != nil {
		return nil, fmt.Errorf("failed to update entity: %v", err)
	}

	if result.MatchedCount == 0 {
		return nil, fmt.Errorf("entity not found")
	}

	// 返回更新后的实体
	return s.GetByID(ctx, id)
}

// Delete 删除实体
func (s *BaseServiceImpl[T]) Delete(ctx context.Context, id string) error {
	objectID, err := bson.ObjectIDFromHex(id)
	if err != nil {
		return fmt.Errorf("invalid ID format: %v", err)
	}

	collection := database.GetCollection[T](s.collectionName)
	result, err := collection.Deleter().Filter(map[string]interface{}{
		"_id": objectID,
	}).DeleteOne(ctx)

	if err != nil {
		return fmt.Errorf("failed to delete entity: %v", err)
	}

	if result.DeletedCount == 0 {
		return fmt.Errorf("entity not found")
	}

	return nil
}

// List 分页查询
func (s *BaseServiceImpl[T]) List(ctx context.Context, params map[string]interface{}) (*response.PagedResponse, error) {
	return s.pagination.QueryWithPagination(ctx, params)
}

// ListAll 查询所有
func (s *BaseServiceImpl[T]) ListAll(ctx context.Context, params map[string]interface{}) ([]T, error) {
	return s.pagination.QueryAll(ctx, params)
}

// setCreateFields 设置创建字段
func (s *BaseServiceImpl[T]) setCreateFields(entity *T) {
	s.setTimeFields(entity, true)
}

// setUpdateFields 设置更新字段
func (s *BaseServiceImpl[T]) setUpdateFields(entity *T) {
	s.setTimeFields(entity, false)
}

// setTimeFields 设置时间字段
func (s *BaseServiceImpl[T]) setTimeFields(entity *T, isCreate bool) {
	v := reflect.ValueOf(entity).Elem()
	t := v.Type()

	for i := 0; i < v.NumField(); i++ {
		field := v.Field(i)
		fieldType := t.Field(i)

		if !field.CanSet() {
			continue
		}

		switch fieldType.Name {
		case "CreatedAt":
			if isCreate && field.IsZero() {
				field.Set(reflect.ValueOf(time.Now()))
			}
		case "UpdatedAt":
			field.Set(reflect.ValueOf(time.Now()))
		case "ID":
			if isCreate && field.IsZero() {
				field.Set(reflect.ValueOf(bson.NewObjectID()))
			}
		}
	}
}

// buildUpdateDocument 构建更新文档
func (s *BaseServiceImpl[T]) buildUpdateDocument(entity *T) map[string]interface{} {
	updateDoc := make(map[string]interface{})

	v := reflect.ValueOf(entity).Elem()
	t := v.Type()

	for i := 0; i < v.NumField(); i++ {
		field := v.Field(i)
		fieldType := t.Field(i)

		// 跳过ID字段和零值字段
		if fieldType.Name == "ID" || field.IsZero() {
			continue
		}

		// 获取bson标签
		bsonTag := fieldType.Tag.Get("bson")
		if bsonTag == "" || bsonTag == "-" {
			continue
		}

		// 解析bson标签
		tagParts := strings.Split(bsonTag, ",")
		fieldName := tagParts[0]
		if fieldName == "" {
			fieldName = strings.ToLower(fieldType.Name)
		}

		updateDoc[fieldName] = field.Interface()
	}

	return updateDoc
}
