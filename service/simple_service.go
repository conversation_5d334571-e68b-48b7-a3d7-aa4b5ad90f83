package service

import (
	"context"
	"demo/database"
	"demo/response"
)

// SimpleService 简化的服务接口
type SimpleService[T any] interface {
	Create(ctx context.Context, entity *T) (*T, error)
	GetByID(ctx context.Context, id string) (*T, error)
	Update(ctx context.Context, id string, entity *T) (*T, error)
	Delete(ctx context.Context, id string) error
	List(ctx context.Context, params map[string]interface{}) (*response.PagedResponse, error)
}

// SimpleServiceImpl 简化的服务实现
type SimpleServiceImpl[T any] struct {
	collectionName string
}

// NewSimpleService 创建简化服务
func NewSimpleService[T any](collectionName string) SimpleService[T] {
	return &SimpleServiceImpl[T]{
		collectionName: collectionName,
	}
}

// Create 创建实体
func (s *SimpleServiceImpl[T]) Create(ctx context.Context, entity *T) (*T, error) {
	_, err := database.Create(s.collectionName, entity)
	if err != nil {
		return nil, err
	}
	return entity, nil
}

// GetByID 根据ID获取实体
func (s *SimpleServiceImpl[T]) GetByID(ctx context.Context, id string) (*T, error) {
	var result T
	err := database.GetByID(s.collectionName, id, &result)
	if err != nil {
		return nil, err
	}
	return &result, nil
}

// Update 更新实体
func (s *SimpleServiceImpl[T]) Update(ctx context.Context, id string, entity *T) (*T, error) {
	err := database.Update(s.collectionName, id, entity)
	if err != nil {
		return nil, err
	}
	return entity, nil
}

// Delete 删除实体
func (s *SimpleServiceImpl[T]) Delete(ctx context.Context, id string) error {
	return database.Delete(s.collectionName, id)
}

// List 分页查询
func (s *SimpleServiceImpl[T]) List(ctx context.Context, params map[string]interface{}) (*response.PagedResponse, error) {
	return database.QueryWithPagination(s.collectionName, params)
}
