package service

import (
	"context"
	"demo/database"
	"demo/model"
	"demo/response"
	"fmt"
	"strings"
	"time"

	"go.mongodb.org/mongo-driver/v2/bson"
)

// TaskService 任务业务逻辑服务
type TaskService struct {
	repository        *database.Repository[model.Task]
	paginationService *PaginationService[model.Task]
}

// NewTaskService 创建任务服务
func NewTaskService() *TaskService {
	return &TaskService{
		repository:        database.NewRepository[model.Task]("task"),
		paginationService: NewPaginationService[model.Task]("task"),
	}
}

// CreateTask 创建任务 - 业务逻辑层
func (ts *TaskService) CreateTask(ctx context.Context, req *CreateTaskRequest) (*model.Task, error) {
	// 1. 业务验证：检查任务名称
	if strings.TrimSpace(req.Name) == "" {
		return nil, fmt.Errorf("任务名称不能为空")
	}

	// 2. 业务验证：检查任务名称长度
	if len(req.Name) > 100 {
		return nil, fmt.Errorf("任务名称不能超过100个字符")
	}

	// 3. 业务验证：检查内容长度
	if len(req.Content) > 1000 {
		return nil, fmt.Errorf("任务内容不能超过1000个字符")
	}

	// 4. 业务规则：检查任务名称是否已存在
	exists, err := ts.isTaskNameExists(ctx, req.Name)
	if err != nil {
		return nil, fmt.Errorf("检查任务名称失败: %v", err)
	}
	if exists {
		return nil, fmt.Errorf("任务名称已存在")
	}

	// 5. 业务逻辑：构建任务实体
	task := &model.Task{
		Name:    strings.TrimSpace(req.Name),
		Content: strings.TrimSpace(req.Content),
	}

	// 设置基础字段
	task.DefaultId()
	task.DefaultCreatedAt()
	task.DefaultUpdatedAt()

	// 6. 调用数据层
	createdTask, err := ts.repository.Create(ctx, task)
	if err != nil {
		return nil, fmt.Errorf("创建任务失败: %v", err)
	}

	return createdTask, nil
}

// GetTaskByID 根据ID获取任务 - 业务逻辑层
func (ts *TaskService) GetTaskByID(ctx context.Context, taskID string) (*model.Task, error) {
	// 1. 业务验证：ID格式检查
	if taskID == "" {
		return nil, fmt.Errorf("任务ID不能为空")
	}

	// 2. 调用数据层
	task, err := ts.repository.FindOne(ctx, bson.M{"_id": taskID})
	if err != nil {
		return nil, fmt.Errorf("任务不存在: %v", err)
	}

	return task, nil
}

// UpdateTask 更新任务 - 业务逻辑层
func (ts *TaskService) UpdateTask(ctx context.Context, taskID string, req *UpdateTaskRequest) (*model.Task, error) {
	// 1. 业务验证：检查任务是否存在
	existingTask, err := ts.repository.FindOne(ctx, bson.M{"_id": taskID})
	if err != nil {
		return nil, fmt.Errorf("任务不存在: %v", err)
	}

	// 2. 业务验证：如果更新名称，检查新名称是否已被其他任务使用
	if req.Name != "" && req.Name != existingTask.Name {
		if len(req.Name) > 100 {
			return nil, fmt.Errorf("任务名称不能超过100个字符")
		}

		exists, err := ts.isTaskNameExistsExcludeTask(ctx, req.Name, taskID)
		if err != nil {
			return nil, fmt.Errorf("检查任务名称失败: %v", err)
		}
		if exists {
			return nil, fmt.Errorf("任务名称已被其他任务使用")
		}
	}

	// 3. 业务验证：检查内容长度
	if req.Content != "" && len(req.Content) > 1000 {
		return nil, fmt.Errorf("任务内容不能超过1000个字符")
	}

	// 4. 业务逻辑：构建更新数据
	updateData := bson.M{
		"updated_at": time.Now(),
	}

	if req.Name != "" {
		updateData["name"] = strings.TrimSpace(req.Name)
	}
	if req.Content != "" {
		updateData["content"] = strings.TrimSpace(req.Content)
	}

	// 5. 调用数据层
	_, err = ts.repository.UpdateByFilter(ctx, bson.M{"_id": taskID}, updateData)
	if err != nil {
		return nil, fmt.Errorf("更新任务失败: %v", err)
	}

	// 6. 返回更新后的任务
	return ts.GetTaskByID(ctx, taskID)
}

// DeleteTask 删除任务 - 业务逻辑层
func (ts *TaskService) DeleteTask(ctx context.Context, taskID string) error {
	// 1. 业务验证：检查任务是否存在
	_, err := ts.repository.FindOne(ctx, bson.M{"_id": taskID})
	if err != nil {
		return fmt.Errorf("任务不存在: %v", err)
	}

	// 2. 调用数据层删除
	_, err = ts.repository.DeleteByFilter(ctx, bson.M{"_id": taskID})
	if err != nil {
		return fmt.Errorf("删除任务失败: %v", err)
	}

	return nil
}

// GetTaskList 获取任务列表 - 业务逻辑层
func (ts *TaskService) GetTaskList(ctx context.Context, params map[string]interface{}) (*response.PageResponse, error) {
	// 1. 业务逻辑：处理查询参数
	processedParams := ts.processQueryParams(params)

	// 2. 调用分页服务
	result, err := ts.paginationService.QueryWithPagination(ctx, processedParams)
	if err != nil {
		return nil, fmt.Errorf("查询任务列表失败: %v", err)
	}

	return result, nil
}

// ==================== 私有业务逻辑方法 ====================

// isTaskNameExists 检查任务名称是否存在 - 业务逻辑
func (ts *TaskService) isTaskNameExists(ctx context.Context, name string) (bool, error) {
	count, err := ts.repository.Count(ctx, bson.M{"name": name})
	return count > 0, err
}

// isTaskNameExistsExcludeTask 检查任务名称是否被其他任务使用 - 业务逻辑
func (ts *TaskService) isTaskNameExistsExcludeTask(ctx context.Context, name, excludeTaskID string) (bool, error) {
	count, err := ts.repository.Count(ctx, bson.M{
		"name": name,
		"_id":  bson.M{"$ne": excludeTaskID},
	})
	return count > 0, err
}

// processQueryParams 处理查询参数 - 业务逻辑
func (ts *TaskService) processQueryParams(params map[string]interface{}) map[string]interface{} {
	processed := make(map[string]interface{})

	for key, value := range params {
		switch key {
		case "name":
			// 任务名称支持模糊搜索
			if str, ok := value.(string); ok && str != "" {
				processed["name__like"] = str
			}
		case "content":
			// 任务内容支持模糊搜索
			if str, ok := value.(string); ok && str != "" {
				processed["content__like"] = str
			}
		default:
			// 其他参数直接传递
			processed[key] = value
		}
	}

	return processed
}

// ==================== 请求结构体 ====================

type CreateTaskRequest struct {
	Name    string `json:"name" binding:"required" validate:"min=1,max=100"`
	Content string `json:"content" validate:"max=1000"`
}

type UpdateTaskRequest struct {
	Name    string `json:"name" validate:"min=1,max=100"`
	Content string `json:"content" validate:"max=1000"`
}
