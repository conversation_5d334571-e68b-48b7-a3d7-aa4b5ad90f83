# 测试MongoDB v2驱动版本的API
Write-Host "Testing MongoDB v2 Driver API..." -ForegroundColor Green

try {
    # 测试健康检查
    Write-Host "`nTesting health endpoint..." -ForegroundColor Yellow
    $healthResponse = Invoke-RestMethod -Uri "http://localhost:5019/health" -Method GET
    Write-Host "✅ Health check successful:" -ForegroundColor Green
    Write-Host ($healthResponse | ConvertTo-Json -Depth 2)
    
    # 测试创建任务
    Write-Host "`nTesting create task..." -ForegroundColor Yellow
    $taskData = @{
        name = "Test Task with MongoDB v2"
        content = "Testing with cleaned up dependencies"
    } | ConvertTo-Json
    
    $createResponse = Invoke-RestMethod -Uri "http://localhost:5019/api/v1/tasks" -Method POST -Body $taskData -ContentType "application/json"
    Write-Host "✅ Task created successfully:" -ForegroundColor Green
    Write-Host ($createResponse | ConvertTo-Json -Depth 3)
    
    $taskId = $createResponse.data.id
    
    # 测试获取任务
    Write-Host "`nTesting get task..." -ForegroundColor Yellow
    $getResponse = Invoke-RestMethod -Uri "http://localhost:5019/api/v1/tasks/$taskId" -Method GET
    Write-Host "✅ Task retrieved successfully:" -ForegroundColor Green
    Write-Host ($getResponse | ConvertTo-Json -Depth 3)
    
    # 测试任务列表
    Write-Host "`nTesting list tasks..." -ForegroundColor Yellow
    $listResponse = Invoke-RestMethod -Uri "http://localhost:5019/api/v1/tasks" -Method GET
    Write-Host "✅ Task list retrieved successfully:" -ForegroundColor Green
    Write-Host ($listResponse | ConvertTo-Json -Depth 3)
    
    Write-Host "`n🎉 All tests passed! MongoDB v2 driver is working correctly!" -ForegroundColor Green
    
} catch {
    Write-Host "❌ Test failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Response: $($_.Exception.Response)" -ForegroundColor Red
}
