package database

import (
	"context"
	"demo/response"
	"fmt"
	"strings"

	"github.com/chenmingyong0423/go-mongox/v2/bsonx"
	"go.mongodb.org/mongo-driver/v2/mongo/options"
)

// PaginationParams 分页参数
type PaginationParams struct {
	Page  int    `form:"page" json:"page"`
	Size  int    `form:"size" json:"size"`
	Order string `form:"order" json:"order"`
}

// Validate 验证分页参数
func (p *PaginationParams) Validate() {
	if p.Page <= 0 {
		p.Page = 1
	}
	if p.Size <= 0 {
		p.Size = 10
	}
	if p.Size > 1000 { // 限制最大页面大小
		p.Size = 1000
	}
	if p.Order == "" {
		p.Order = "-_id"
	}
}

// GetSkip 获取跳过的记录数
func (p *PaginationParams) GetSkip() int64 {
	return int64(p.Size * (p.Page - 1))
}

// GetLimit 获取限制的记录数
func (p *PaginationParams) GetLimit() int64 {
	return int64(p.<PERSON>ze)
}

// BuildSortOptions 构建排序选项
func (p *PaginationParams) BuildSortOptions() *bsonx.D {
	sortBsonx := bsonx.NewD()
	orderFields := strings.Split(p.Order, ",")

	for _, field := range orderFields {
		field = strings.TrimSpace(field)
		direction := 1

		if strings.HasPrefix(field, "-") {
			direction = -1
			field = strings.TrimPrefix(field, "-")
		} else if strings.HasPrefix(field, "+") {
			field = strings.TrimPrefix(field, "+")
		}

		sortBsonx = sortBsonx.Add(field, direction)
	}

	return sortBsonx
}

// PaginationService 分页服务
type PaginationService[T any] struct {
	collectionName string
}

// NewPaginationService 创建分页服务
func NewPaginationService[T any](collectionName string) *PaginationService[T] {
	return &PaginationService[T]{
		collectionName: collectionName,
	}
}

// QueryWithPagination 带分页的查询
func (ps *PaginationService[T]) QueryWithPagination(
	ctx context.Context,
	params map[string]interface{},
) (*response.PageResponse, error) {
	// 提取分页参数
	pagination := ps.extractPaginationParams(params)
	pagination.Validate()

	// 构建查询条件
	queryBuilder := NewQueryBuilder()
	query := queryBuilder.BuildFromMap(params)

	// 检查查询构建错误
	if queryBuilder.HasErrors() {
		return nil, fmt.Errorf("query build errors: %v", queryBuilder.GetErrors())
	}

	// 获取集合
	collection := GetCollection[T](ps.collectionName)

	// 构建排序选项
	sortOptions := pagination.BuildSortOptions()

	// 执行查询
	results, err := collection.Finder().
		Filter(query.Build()).
		Find(ctx,
			options.Find().SetSort(sortOptions),
			options.Find().SetSkip(pagination.GetSkip()),
			options.Find().SetLimit(pagination.GetLimit()),
		)
	if err != nil {
		return nil, fmt.Errorf("failed to execute query: %v", err)
	}

	// 获取总数
	total, err := collection.Finder().Filter(query.Build()).Count(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to count documents: %v", err)
	}

	// 构建响应
	pageResponse := response.SuccessPage(results, pagination.Page, pagination.Size, total)
	return &pageResponse, nil
}

// extractPaginationParams 提取分页参数
func (ps *PaginationService[T]) extractPaginationParams(params map[string]interface{}) *PaginationParams {
	pagination := &PaginationParams{
		Page:  1,
		Size:  10,
		Order: "-_id",
	}

	if page, ok := params["page"].(int); ok {
		pagination.Page = page
	}
	if size, ok := params["size"].(int); ok {
		pagination.Size = size
	}
	if order, ok := params["order"].(string); ok {
		pagination.Order = order
	}

	return pagination
}

// QueryAll 查询所有记录（不分页）
func (ps *PaginationService[T]) QueryAll(
	ctx context.Context,
	params map[string]interface{},
) ([]T, error) {
	// 构建查询条件
	queryBuilder := NewQueryBuilder()
	query := queryBuilder.BuildFromMap(params)

	// 检查查询构建错误
	if queryBuilder.HasErrors() {
		return nil, fmt.Errorf("query build errors: %v", queryBuilder.GetErrors())
	}

	// 获取集合
	collection := GetCollection[T](ps.collectionName)

	// 执行查询
	results, err := collection.Finder().Filter(query.Build()).Find(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to execute query: %v", err)
	}

	return results, nil
}

// QueryOne 查询单个记录
func (ps *PaginationService[T]) QueryOne(
	ctx context.Context,
	params map[string]interface{},
) (*T, error) {
	// 构建查询条件
	queryBuilder := NewQueryBuilder()
	query := queryBuilder.BuildFromMap(params)

	// 检查查询构建错误
	if queryBuilder.HasErrors() {
		return nil, fmt.Errorf("query build errors: %v", queryBuilder.GetErrors())
	}

	// 获取集合
	collection := GetCollection[T](ps.collectionName)

	// 执行查询
	result, err := collection.Finder().Filter(query.Build()).FindOne(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to execute query: %v", err)
	}

	return result, nil
}
