package database

import (
	"context"
	"demo/response"
	"fmt"
	"github.com/chenmingyong0423/go-mongox/v2"
	"github.com/chenmingyong0423/go-mongox/v2/bsonx"
	"github.com/chenmingyong0423/go-mongox/v2/builder/query"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/v2/bson"
	"go.mongodb.org/mongo-driver/v2/mongo"
	"go.mongodb.org/mongo-driver/v2/mongo/options"
	"go.mongodb.org/mongo-driver/v2/mongo/readpref"
	"log"
	"reflect"
	"regexp"
	"strconv"
	"strings"
	"time"
)

type mongoxHelper struct {
	Host     string
	Port     int
	DBName   string
	Username string
	Password string
}

func GetMongoxHelper() mongoxHelper {
	return mongoxHelper{common.MongodbHost, common.MongodbPort, common.MongodbDbName, common.MongodbUser, common.MongodbPassword}
}

func (helper mongoxHelper) ConnDb() (*mongo.Client, error) {
	fmt.Println("mongodb://" + helper.Host + ":" + strconv.Itoa(helper.Port))
	client, err := mongo.Connect(
		options.Client().
			ApplyURI("mongodb://" + helper.Host + ":" + strconv.Itoa(helper.Port)).
			SetAuth(options.Credential{
				Username: helper.Username,
				Password: helper.Password,
			}))
	if err != nil {
		log.Fatal(err)
		return nil, fmt.Errorf("failed to connect to MongoDB: %v", err)
	}
	err = client.Ping(context.Background(), readpref.Primary())
	if err != nil {
		log.Fatal(err)
		return nil, fmt.Errorf("failed to connect to MongoDB: %v", err)
	}
	return client, err
}

func NewCollection[T any](collectionName string) *mongox.Collection[T] {
	mh := GetMongoxHelper()
	mongoClient, err := mh.ConnDb()
	if err != nil {
		return nil
	}
	client := mongox.NewClient(mongoClient, &mongox.Config{})
	database := client.NewDatabase(mh.DBName)
	mongoxColl := mongox.NewCollection[T](database, collectionName)
	return mongoxColl
}

var EQUAL_FIELDS = []string{
	"task_id", "task_tag", "ip_type", "scope_id", "type",
}

func getRawValue(queryArgs map[string]interface{}, key string) map[string]interface{} {
	if value, ok := queryArgs[key]; ok {
		return value.(map[string]interface{})
	}
	return make(map[string]interface{})
}
func contains(s []string, str string) bool {
	for _, v := range s {
		if v == str {
			return true
		}
	}
	return false
}

func GetQueryPageResponse[T any](collectionName string, args map[string]interface{},
	opts ...*options.FindOptions) (interface{}, error) {

	ret := map[string]interface{}{
		"page":  1,
		"size":  10,
		"order": "-_id",
	}

	for key := range ret {
		if val, ok := args[key]; ok && val != nil {
			ret[key] = val
		}
	}

	size, _ := ret["size"].(int)
	if size <= 0 {
		size = 10
	} else if size >= 100000 {
		size = 100000
	}

	page, _ := ret["page"].(int)
	if page <= 0 {
		page = 1
	}

	orderFieldStr, _ := ret["order"].(string)
	orderByFields := strings.Split(orderFieldStr, ",")

	sortBsonx := bsonx.NewD()
	for _, field := range orderByFields {
		field = strings.TrimSpace(field)
		direction := 1
		if strings.HasPrefix(field, "-") {
			direction = -1
			field = strings.TrimPrefix(field, "-")
		} else if strings.HasPrefix(field, "+") {
			field = strings.TrimPrefix(field, "+")
		}
		sortBsonx = sortBsonx.Add(field, direction)

	}
	fmt.Println(sortBsonx)

	queryBuilder := BuildDbQuery(args)

	skipNum := int64(size * (page - 1))
	limitNum := int64(size)

	//findOptions := &options.FindOptions{
	//	Sort:  sortBsonx.Build(),
	//	Skip:  &skipNum,
	//	Limit: &limitNum,
	//}

	coll := NewCollection[T](collectionName)
	results, err2 := coll.Finder().Filter(queryBuilder.Build()).Find(context.Background(),
		options.Find().SetSort(sortBsonx),
		options.Find().SetSkip(skipNum),
		options.Find().SetLimit(limitNum),
	)
	if err2 != nil {
		return nil, err2
	}

	count, err3 := coll.Finder().Filter(queryBuilder.Build()).Count(context.Background())
	if err3 != nil {
		return nil, err2
	}
	fmt.Println(results, page, size, count)

	return response.SuccessPage(results, page, size, count), nil
}

func BuildDbQuery(args map[string]interface{}) *query.Builder {
	var baseQueryFields = map[string]interface{}{
		"page":  1,
		"size":  10,
		"order": "_id",
	}
	queryBuilder := query.NewBuilder()
	queryArgs := make(map[string]interface{})
	for key, value := range args {

		if _, ok := baseQueryFields[key]; ok {
			continue
		}

		if key == "id" && value != nil {
			queryArgs["_id"], _ = primitive.ObjectIDFromHex(value.(string))

			id, _ := primitive.ObjectIDFromHex(value.(string))
			queryBuilder = queryBuilder.Id(id)

			continue
		}

		if key == "_id" && value != nil {
			queryArgs[key], _ = primitive.ObjectIDFromHex(value.(string))

			id, _ := primitive.ObjectIDFromHex(value.(string))
			queryBuilder = queryBuilder.Id(id)
			continue
		}

		if strings.HasSuffix(key, "_id") && value != nil && reflect.TypeOf(value).Kind() == reflect.String {
			if value == "" {
				continue
			}
			// 如果字段名以 "_id" 结尾，则尝试转换为 ObjectID
			queryArgs[key], _ = primitive.ObjectIDFromHex(value.(string))

			id, _ := primitive.ObjectIDFromHex(value.(string))
			queryBuilder = queryBuilder.Eq(key, id)
			continue
		}

		if value == nil {
			continue
		}

		switch {
		case strings.HasSuffix(key, "__eq"):
			realKey := strings.TrimSuffix(key, "__eq")
			if value != "" {
				queryBuilder = queryBuilder.Eq(realKey, value)
			}
		case strings.HasSuffix(key, "__dgt"):
			realKey := strings.TrimSuffix(key, "__dgt")
			rawValue := getRawValue(queryArgs, realKey)
			date, _ := time.Parse("2006-01-02 15:04:05", value.(string))
			rawValue["$gt"] = date
			queryArgs[realKey] = rawValue

			queryBuilder = queryBuilder.Gt(realKey, date)

		case strings.HasSuffix(key, "__dlt"):
			realKey := strings.TrimSuffix(key, "__dlt")
			rawValue := getRawValue(queryArgs, realKey)
			date, _ := time.Parse("2006-01-02 15:04:05", value.(string))
			rawValue["$lt"] = date
			queryArgs[realKey] = rawValue

			queryBuilder = queryBuilder.Lt(realKey, date)

		case strings.HasSuffix(key, "__neq"):
			realKey := strings.TrimSuffix(key, "__neq")
			queryArgs[realKey] = map[string]interface{}{
				"$ne": value,
			}

			queryBuilder = queryBuilder.Ne(realKey, value)

		case strings.HasSuffix(key, "__not"):
			realKey := strings.TrimSuffix(key, "__not")
			queryArgs[realKey] = map[string]interface{}{
				"$not": regexp.MustCompile(regexp.QuoteMeta(value.(string))),
			}

			// 不确定正常不正常
			queryBuilder = queryBuilder.Not(query.Regex(realKey, regexp.QuoteMeta(value.(string))))

		case strings.HasSuffix(key, "__in"):
			realKey := strings.TrimSuffix(key, "__in")
			inDataList := value.([]interface{})
			if len(inDataList) == 0 {
				continue
			}
			queryArgs[realKey] = map[string]interface{}{
				"$in": inDataList,
			}

			// 不确定正常不正常
			queryBuilder = queryBuilder.In(realKey, inDataList)

		case strings.HasSuffix(key, "__in_like"):
			realKey := strings.TrimSuffix(key, "__in_like")
			inDataList := value.([]interface{})
			if len(inDataList) == 0 {
				continue
			}

			elemMatchList := make([]map[string]interface{}, 0, len(inDataList))
			for _, inData := range inDataList {
				elemMatchList = append(elemMatchList, map[string]interface{}{
					realKey: map[string]interface{}{
						"$elemMatch": map[string]interface{}{
							"$regex":   fmt.Sprintf(".*%s.*", inData),
							"$options": "i",
						},
					},
				})
			}

			queryArgs["$or"] = elemMatchList

			// 太复杂了，暂时不改
			//queryBuilder = queryBuilder

		case reflect.TypeOf(value).Kind() == reflect.String:
			if value == "" {
				break
			}
			if contains(EQUAL_FIELDS, key) {
				queryArgs[key] = value

				queryBuilder = queryBuilder.Eq(key, value)
			} else {
				queryArgs[key] = map[string]interface{}{
					"$regex":   regexp.QuoteMeta(value.(string)),
					"$options": "i",
				}

				// 不确定正常不正常
				queryBuilder = queryBuilder.RegexOptions(key, regexp.QuoteMeta(value.(string)), "i")
			}

		default:
			queryArgs[key] = value

			queryBuilder = queryBuilder.Eq(key, value)
		}
	}

	//fmt.Println(queryArgs)
	//return queryArgs
	return queryBuilder
}

// 将驼峰命名转换为下划线命名
func camelToSnake(s string) string {
	s = regexp.MustCompile("([a-z])([A-Z])").ReplaceAllString(s, "${1}_${2}")
	return strings.ToLower(s)
}

func StructToBsonM(data interface{}) bson.M {
	result := bson.M{}
	dataValue := reflect.ValueOf(data)
	dataType := reflect.TypeOf(data)

	if dataValue.Kind() == reflect.Ptr {
		dataValue = dataValue.Elem()
		dataType = dataType.Elem()
	}

	for i := 0; i < dataValue.NumField(); i++ {
		field := dataType.Field(i)
		fieldValue := dataValue.Field(i).Interface()

		// 当字段名为"id"时（不区分大小写），对应到bson的"_id"
		fieldName := field.Name
		if strings.ToLower(fieldName) == "id" {
			fieldName = "_id"
		} else {
			fieldName = camelToSnake(field.Name) // 将其他字段名从驼峰转换为下划线
		}

		switch reflect.TypeOf(fieldValue).Kind() {
		case reflect.Struct:
			result[fieldName] = StructToBsonM(fieldValue)
		default:
			if ptr, ok := fieldValue.(*string); ok && ptr != nil {
				result[fieldName] = handleStringField(*ptr, fieldName)
			} else if str, ok := fieldValue.(string); ok {
				result[fieldName] = handleStringField(str, fieldName)
			} else if fieldValue != nil {
				result[fieldName] = fieldValue
			}
		}
	}

	return result
}

func handleStringField(str string, fieldName string) interface{} {
	if strings.HasSuffix(fieldName, "_id") {
		// 如果字段名以 "_id" 结尾，则尝试转换为 ObjectID
		objectId, err := primitive.ObjectIDFromHex(str)
		if err == nil {
			return objectId
		}
	}
	return str
}
