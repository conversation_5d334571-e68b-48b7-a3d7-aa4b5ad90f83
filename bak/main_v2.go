package main

import (
	"context"
	"demo/bak"
	"demo/config"
	"demo/database"
	"demo/router/middleware"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
)

func main() {
	// 初始化数据库连接
	if err := database.InitMongoDB(); err != nil {
		log.Fatal("Failed to initialize database:", err)
	}
	defer database.Close()

	// 设置Gin模式
	gin.SetMode(config.AppConfig.Server.Mode)

	// 创建Gin引擎
	router := gin.New()

	// 注册中间件
	setupMiddleware(router)

	// 注册路由
	setupRoutes(router)

	// 创建HTTP服务器
	server := &http.Server{
		Addr:         ":" + config.AppConfig.Server.Port,
		Handler:      router,
		ReadTimeout:  30 * time.Second,
		WriteTimeout: 30 * time.Second,
		IdleTimeout:  60 * time.Second,
	}

	// 启动服务器
	go func() {
		log.Printf("Server starting on port %s", config.AppConfig.Server.Port)
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatal("Failed to start server:", err)
		}
	}()

	// 优雅关闭
	gracefulShutdown(server)
}

// setupMiddleware 设置中间件
func setupMiddleware(router *gin.Engine) {
	// 基础中间件
	router.Use(middleware.RequestID())
	router.Use(middleware.Logger())
	router.Use(middleware.Recovery())
	router.Use(middleware.Security())

	// CORS中间件
	config := cors.DefaultConfig()
	config.AllowMethods = []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"}
	config.AllowHeaders = []string{"Origin", "Content-Type", "Authorization", "X-Request-ID"}
	config.AllowOrigins = []string{
		"http://localhost:3000",
		"http://localhost:8080",
		"http://localhost:5173",
		// 添加更多允许的源
	}
	config.AllowCredentials = true
	router.Use(cors.New(config))

	// 速率限制中间件（可选）
	// router.Use(middleware.RateLimiter(100, time.Minute))

	// 请求超时中间件（可选）
	// router.Use(middleware.Timeout(30 * time.Second))

	// JSON验证中间件
	router.Use(middleware.ValidateJSON())
}

// setupRoutes 设置路由
func setupRoutes(router *gin.Engine) {
	// 健康检查
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":    "ok",
			"timestamp": time.Now().Unix(),
		})
	})

	// API路由组
	apiV1 := router.Group("/api/v1")
	{
		// 注册任务控制器路由
		taskController := bak.NewTaskController()
		taskController.RegisterRoutes(apiV1)

		// 可以在这里注册更多控制器
		// userController := controller.NewUserController()
		// userController.RegisterRoutes(apiV1)
	}

	// 404处理
	router.NoRoute(func(c *gin.Context) {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "Route not found",
		})
	})
}

// gracefulShutdown 优雅关闭
func gracefulShutdown(server *http.Server) {
	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	log.Println("Shutting down server...")

	// 设置关闭超时
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 关闭服务器
	if err := server.Shutdown(ctx); err != nil {
		log.Fatal("Server forced to shutdown:", err)
	}

	log.Println("Server exited")
}
