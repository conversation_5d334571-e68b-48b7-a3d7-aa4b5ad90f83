package controller

import (
	"demo/model"
	"demo/response"
	"demo/service"
	"demo/validator"
	"net/http"

	"github.com/gin-gonic/gin"
)

// TaskController 任务控制器
type TaskController struct {
	*BaseController[model.Task]
	taskService service.BaseService[model.Task]
}

// TaskCreateRequest 创建任务请求
type TaskCreateRequest struct {
	Name    string `json:"name" validate:"required,min=1,max=100"`
	Content string `json:"content" validate:"required,min=1,max=1000"`
}

// TaskUpdateRequest 更新任务请求
type TaskUpdateRequest struct {
	Name    string `json:"name,omitempty" validate:"omitempty,min=1,max=100"`
	Content string `json:"content,omitempty" validate:"omitempty,min=1,max=1000"`
}

// TaskQueryRequest 查询任务请求
type TaskQueryRequest struct {
	Name__like string `form:"name__like"`
	Content    string `form:"content"`
	Page       int    `form:"page"`
	Size       int    `form:"size"`
	Order      string `form:"order"`
}

// NewTaskController 创建任务控制器
func NewTaskController() *TaskController {
	taskService := service.NewBaseService[model.Task]("task")
	baseController := NewBaseController(taskService)
	
	return &TaskController{
		BaseController: baseController,
		taskService:    taskService,
	}
}

// CreateTask 创建任务
func (tc *TaskController) CreateTask(c *gin.Context) {
	var req TaskCreateRequest
	
	// 绑定并验证请求
	if err := validator.BindAndValidate(c, &req); err != nil {
		if validationErrors := validator.GetValidationErrors(err); len(validationErrors) > 0 {
			resp := validator.NewValidationErrorResponse(err)
			c.JSON(http.StatusBadRequest, resp)
			return
		}
		tc.RespondWithError(c, http.StatusBadRequest, "Invalid request body")
		return
	}

	// 创建任务实体
	task := &model.Task{
		Name:    req.Name,
		Content: req.Content,
	}

	// 调用服务创建任务
	result, err := tc.taskService.Create(c.Request.Context(), task)
	if err != nil {
		tc.RespondWithError(c, http.StatusInternalServerError, "Failed to create task")
		return
	}

	tc.RespondWithSuccess(c, result, "Task created successfully")
}

// UpdateTask 更新任务
func (tc *TaskController) UpdateTask(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		tc.RespondWithError(c, http.StatusBadRequest, "Task ID is required")
		return
	}

	var req TaskUpdateRequest
	
	// 绑定并验证请求
	if err := validator.BindAndValidate(c, &req); err != nil {
		if validationErrors := validator.GetValidationErrors(err); len(validationErrors) > 0 {
			resp := validator.NewValidationErrorResponse(err)
			c.JSON(http.StatusBadRequest, resp)
			return
		}
		tc.RespondWithError(c, http.StatusBadRequest, "Invalid request body")
		return
	}

	// 获取现有任务
	existingTask, err := tc.taskService.GetByID(c.Request.Context(), id)
	if err != nil {
		tc.RespondWithError(c, http.StatusNotFound, "Task not found")
		return
	}

	// 更新字段
	if req.Name != "" {
		existingTask.Name = req.Name
	}
	if req.Content != "" {
		existingTask.Content = req.Content
	}

	// 调用服务更新任务
	result, err := tc.taskService.Update(c.Request.Context(), id, existingTask)
	if err != nil {
		tc.RespondWithError(c, http.StatusInternalServerError, "Failed to update task")
		return
	}

	tc.RespondWithSuccess(c, result, "Task updated successfully")
}

// GetTask 获取单个任务
func (tc *TaskController) GetTask(c *gin.Context) {
	tc.GetByID(c) // 使用基类方法
}

// DeleteTask 删除任务
func (tc *TaskController) DeleteTask(c *gin.Context) {
	tc.Delete(c) // 使用基类方法
}

// ListTasks 分页查询任务
func (tc *TaskController) ListTasks(c *gin.Context) {
	var req TaskQueryRequest
	
	// 绑定查询参数
	if err := c.ShouldBindQuery(&req); err != nil {
		tc.RespondWithError(c, http.StatusBadRequest, "Invalid query parameters")
		return
	}

	// 构建查询参数
	params := map[string]interface{}{
		"page":  req.Page,
		"size":  req.Size,
		"order": req.Order,
	}

	if req.Name__like != "" {
		params["name__like"] = req.Name__like
	}
	if req.Content != "" {
		params["content"] = req.Content
	}

	// 调用服务查询
	result, err := tc.taskService.List(c.Request.Context(), params)
	if err != nil {
		tc.RespondWithError(c, http.StatusInternalServerError, "Failed to query tasks")
		return
	}

	c.JSON(http.StatusOK, result)
}

// GetTaskStats 获取任务统计信息
func (tc *TaskController) GetTaskStats(c *gin.Context) {
	// 这里可以实现自定义的统计逻辑
	// 例如：任务总数、今日创建任务数等
	
	allTasks, err := tc.taskService.ListAll(c.Request.Context(), map[string]interface{}{})
	if err != nil {
		tc.RespondWithError(c, http.StatusInternalServerError, "Failed to get task statistics")
		return
	}

	stats := map[string]interface{}{
		"total_tasks": len(allTasks),
		// 可以添加更多统计信息
	}

	tc.RespondWithSuccess(c, stats, "Task statistics retrieved successfully")
}

// RegisterRoutes 注册路由
func (tc *TaskController) RegisterRoutes(router *gin.RouterGroup) {
	taskGroup := router.Group("/tasks")
	{
		taskGroup.POST("", tc.CreateTask)
		taskGroup.GET("/:id", tc.GetTask)
		taskGroup.PUT("/:id", tc.UpdateTask)
		taskGroup.DELETE("/:id", tc.DeleteTask)
		taskGroup.GET("", tc.ListTasks)
		taskGroup.GET("/stats", tc.GetTaskStats)
	}
}
