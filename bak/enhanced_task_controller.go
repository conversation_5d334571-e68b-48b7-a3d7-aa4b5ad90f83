package controller

import (
	"context"
	"demo/database"
	"demo/model"
	"demo/response"
	"demo/router"
	"demo/validator"
	"net/http"

	"github.com/gin-gonic/gin"
)

// EnhancedTaskController 增强的任务控制器 - 结合原设计优点
type EnhancedTaskController struct{}

var enhancedTaskController = EnhancedTaskController{}

// ==================== 查询任务 ====================
type taskQueryParam struct {
	Name__like string `form:"name" validate:"omitempty,min=1,max=100"`
	Content    string `form:"content" validate:"omitempty,min=1,max=500"`
	Page       int    `form:"page" validate:"omitempty,min=1"`
	Size       int    `form:"size" validate:"omitempty,min=1,max=100"`
	Order      string `form:"order" validate:"omitempty"`
}

// 路由注册 - 保持你原来的风格
func init() {
	router.Api.GET("/task/query", enhancedTaskController.GetTaskList)
}

func (tc *EnhancedTaskController) GetTaskList(c *gin.Context) {
	var param taskQueryParam
	
	// 绑定查询参数
	if err := c.ShouldBindQuery(&param); err != nil {
		resp := response.Fail("Invalid query parameters: " + err.Error())
		c.JSON(http.StatusBadRequest, resp)
		return
	}

	// 验证参数
	if err := validator.Validate(param); err != nil {
		validationResp := validator.NewValidationErrorResponse(err)
		c.JSON(http.StatusBadRequest, validationResp)
		return
	}

	// 使用优化后的分页服务，但保持 StructToBsonM 的便利性
	paginationService := database.NewPaginationService[model.Task]("task")
	queryParams := database.StructToBsonM(param) // 保持你原来的便利方法
	
	pageResponse, err := paginationService.QueryWithPagination(context.Background(), queryParams)
	if err != nil {
		resp := response.Fail("Query failed: " + err.Error())
		c.JSON(http.StatusInternalServerError, resp)
		return
	}

	c.JSON(http.StatusOK, pageResponse)
}

// ==================== 创建任务 ====================
type taskCreateParam struct {
	Name    string `json:"name" validate:"required,min=1,max=100"`
	Content string `json:"content" validate:"required,min=1,max=1000"`
}

func init() {
	router.Api.POST("/task/create", enhancedTaskController.CreateTask)
}

func (tc *EnhancedTaskController) CreateTask(c *gin.Context) {
	var param taskCreateParam
	
	// 绑定JSON参数
	if err := c.ShouldBindJSON(&param); err != nil {
		resp := response.Fail("Invalid request body: " + err.Error())
		c.JSON(http.StatusBadRequest, resp)
		return
	}

	// 验证参数
	if err := validator.Validate(param); err != nil {
		validationResp := validator.NewValidationErrorResponse(err)
		c.JSON(http.StatusBadRequest, validationResp)
		return
	}

	// 创建任务实体
	task := model.Task{
		Name:    param.Name,
		Content: param.Content,
	}

	// 使用优化后的连接管理
	collection := database.GetCollection[model.Task]("task")
	result, err := collection.Creator().InsertOne(context.Background(), &task)
	if err != nil {
		resp := response.Fail("Failed to create task: " + err.Error())
		c.JSON(http.StatusInternalServerError, resp)
		return
	}

	resp := response.SuccessWithMessage(result, "Task created successfully")
	c.JSON(http.StatusOK, resp)
}

// ==================== 更新任务 ====================
type taskUpdateParam struct {
	ID      string `json:"id" validate:"required,objectid"`
	Name    string `json:"name" validate:"omitempty,min=1,max=100"`
	Content string `json:"content" validate:"omitempty,min=1,max=1000"`
}

func init() {
	router.Api.PUT("/task/update", enhancedTaskController.UpdateTask)
}

func (tc *EnhancedTaskController) UpdateTask(c *gin.Context) {
	var param taskUpdateParam
	
	// 绑定JSON参数
	if err := c.ShouldBindJSON(&param); err != nil {
		resp := response.Fail("Invalid request body: " + err.Error())
		c.JSON(http.StatusBadRequest, resp)
		return
	}

	// 验证参数
	if err := validator.Validate(param); err != nil {
		validationResp := validator.NewValidationErrorResponse(err)
		c.JSON(http.StatusBadRequest, validationResp)
		return
	}

	// 构建更新文档 - 使用你原来的便利方法
	updateDoc := database.StructToBsonM(param)
	delete(updateDoc, "_id") // 移除ID字段

	collection := database.GetCollection[model.Task]("task")
	
	// 构建查询条件
	queryBuilder := database.NewQueryBuilder()
	filter := queryBuilder.BuildFromMap(map[string]interface{}{
		"id": param.ID,
	})

	result, err := collection.Updater().Filter(filter.Build()).Updates(updateDoc).UpdateOne(context.Background())
	if err != nil {
		resp := response.Fail("Failed to update task: " + err.Error())
		c.JSON(http.StatusInternalServerError, resp)
		return
	}

	if result.MatchedCount == 0 {
		resp := response.NotFound("Task not found")
		c.JSON(http.StatusNotFound, resp)
		return
	}

	resp := response.SuccessWithMessage(nil, "Task updated successfully")
	c.JSON(http.StatusOK, resp)
}

// ==================== 删除任务 ====================
type taskDeleteParam struct {
	ID string `json:"id" validate:"required,objectid"`
}

func init() {
	router.Api.DELETE("/task/delete", enhancedTaskController.DeleteTask)
}

func (tc *EnhancedTaskController) DeleteTask(c *gin.Context) {
	var param taskDeleteParam
	
	// 绑定JSON参数
	if err := c.ShouldBindJSON(&param); err != nil {
		resp := response.Fail("Invalid request body: " + err.Error())
		c.JSON(http.StatusBadRequest, resp)
		return
	}

	// 验证参数
	if err := validator.Validate(param); err != nil {
		validationResp := validator.NewValidationErrorResponse(err)
		c.JSON(http.StatusBadRequest, validationResp)
		return
	}

	collection := database.GetCollection[model.Task]("task")
	
	// 构建查询条件
	queryBuilder := database.NewQueryBuilder()
	filter := queryBuilder.BuildFromMap(map[string]interface{}{
		"id": param.ID,
	})

	result, err := collection.Deleter().Filter(filter.Build()).DeleteOne(context.Background())
	if err != nil {
		resp := response.Fail("Failed to delete task: " + err.Error())
		c.JSON(http.StatusInternalServerError, resp)
		return
	}

	if result.DeletedCount == 0 {
		resp := response.NotFound("Task not found")
		c.JSON(http.StatusNotFound, resp)
		return
	}

	resp := response.SuccessWithMessage(nil, "Task deleted successfully")
	c.JSON(http.StatusOK, resp)
}

// ==================== 获取单个任务 ====================
type taskGetParam struct {
	ID string `form:"id" validate:"required,objectid"`
}

func init() {
	router.Api.GET("/task/get", enhancedTaskController.GetTask)
}

func (tc *EnhancedTaskController) GetTask(c *gin.Context) {
	var param taskGetParam
	
	// 绑定查询参数
	if err := c.ShouldBindQuery(&param); err != nil {
		resp := response.Fail("Invalid query parameters: " + err.Error())
		c.JSON(http.StatusBadRequest, resp)
		return
	}

	// 验证参数
	if err := validator.Validate(param); err != nil {
		validationResp := validator.NewValidationErrorResponse(err)
		c.JSON(http.StatusBadRequest, validationResp)
		return
	}

	collection := database.GetCollection[model.Task]("task")
	
	// 构建查询条件
	queryBuilder := database.NewQueryBuilder()
	filter := queryBuilder.BuildFromMap(map[string]interface{}{
		"id": param.ID,
	})

	result, err := collection.Finder().Filter(filter.Build()).FindOne(context.Background())
	if err != nil {
		resp := response.NotFound("Task not found")
		c.JSON(http.StatusNotFound, resp)
		return
	}

	resp := response.Success(result)
	c.JSON(http.StatusOK, resp)
}
