package controller

import (
	"context"
	"demo/database"
	"demo/model"
	"demo/response"
	"demo/router"
	"fmt"
	"github.com/gin-gonic/gin"
)

type TaskController struct{}

var taskController = TaskController{}

/*
查询
*/
type taskQueryParam struct {
	Name__eq string `form:"name"`
	Content  string `form:"content"`
	Page     int    `form:"page"`
	Size     int    `form:"size"`
}

func init() {
	router.Api.GET("/task/query", taskController.GetTaskList)
}
func (g *TaskController) GetTaskList(c *gin.Context) {
	var param taskQueryParam
	err := c.BindQuery(&param)
	if err != nil {
		return
	}

	find, err := database.NewCollection[model.Task]("task").Finder().Find(context.Background())
	if err != nil {
		return
	}
	fmt.Println(find)
	pageResponse, _ := database.GetQueryPageResponse[model.Task]("task", database.StructToBsonM(param))
	if err != nil {
		return
	}
	c.<PERSON>(200, pageResponse)
	return
}

/*
创建任务
*/
type taskCreateParam struct {
	Name    string `json:"name"`
	Content string `json:"Content"`
}

func init() {
	router.Api.POST("/task/create", taskController.CreateTask)
}
func (g *TaskController) CreateTask(c *gin.Context) {
	var param taskCreateParam
	err := c.BindJSON(&param)
	if err != nil {
		return
	}

	task := model.Task{
		Name:    param.Name,
		Content: param.Content,
	}

	_, err = database.NewCollection[model.Task]("task").Creator().InsertOne(context.Background(), &task)
	if err != nil {
		c.JSON(200, response.Fail(err.Error()))
		return
	}

	c.JSON(200, response.Success(nil, ""))
	return
}
