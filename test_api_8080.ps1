Write-Host "Testing API on port 8080..." -ForegroundColor Yellow

try {
    # 1. Health Check
    Write-Host "`n1. Testing health endpoint..." -ForegroundColor Cyan
    $health = Invoke-RestMethod -Uri "http://localhost:8080/health" -Method GET
    Write-Host "✅ Health check successful!" -ForegroundColor Green
    $health | ConvertTo-Json
    
    # 2. Create Task
    Write-Host "`n2. Testing create task..." -ForegroundColor Cyan
    $taskData = @{
        name = "MongoDB Integration Test"
        content = "Testing with database abstraction layer - fallback to memory DB"
    } | ConvertTo-Json
    
    $createResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/v1/tasks" -Method POST -Body $taskData -ContentType "application/json"
    Write-Host "✅ Task created successfully!" -ForegroundColor Green
    $taskId = $createResponse.data.id
    Write-Host "Task ID: $taskId" -ForegroundColor Yellow
    
    # 3. Get Task
    Write-Host "`n3. Testing get task..." -ForegroundColor Cyan
    $getResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/v1/tasks/$taskId" -Method GET
    Write-Host "✅ Task retrieved successfully!" -ForegroundColor Green
    $getResponse.data | ConvertTo-Json
    
    # 4. Update Task
    Write-Host "`n4. Testing update task..." -ForegroundColor Cyan
    $updateData = @{
        name = "Updated MongoDB Integration Test"
        content = "Updated content - memory database working perfectly"
    } | ConvertTo-Json
    
    $updateResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/v1/tasks/$taskId" -Method PUT -Body $updateData -ContentType "application/json"
    Write-Host "✅ Task updated successfully!" -ForegroundColor Green
    
    # 5. List Tasks
    Write-Host "`n5. Testing list tasks..." -ForegroundColor Cyan
    $listResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/v1/tasks" -Method GET
    Write-Host "✅ Tasks listed successfully!" -ForegroundColor Green
    Write-Host "Total tasks: $($listResponse.pagination.total)" -ForegroundColor Yellow
    
    # 6. Get Task Stats
    Write-Host "`n6. Testing task stats..." -ForegroundColor Cyan
    $statsResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/v1/tasks/stats" -Method GET
    Write-Host "✅ Task stats retrieved successfully!" -ForegroundColor Green
    $statsResponse.data | ConvertTo-Json
    
    # 7. Delete Task
    Write-Host "`n7. Testing delete task..." -ForegroundColor Cyan
    $deleteResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/v1/tasks/$taskId" -Method DELETE
    Write-Host "✅ Task deleted successfully!" -ForegroundColor Green
    
    # 8. Verify Deletion
    Write-Host "`n8. Verifying task deletion..." -ForegroundColor Cyan
    try {
        $verifyResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/v1/tasks/$taskId" -Method GET
        Write-Host "❌ Task should have been deleted!" -ForegroundColor Red
    } catch {
        Write-Host "✅ Task successfully deleted (404 expected)!" -ForegroundColor Green
    }
    
    Write-Host "`n🎉 All API tests passed!" -ForegroundColor Green
    Write-Host "✅ Database abstraction layer working correctly" -ForegroundColor Green
    Write-Host "✅ Memory database fallback functioning properly" -ForegroundColor Green
    Write-Host "✅ All CRUD operations successful" -ForegroundColor Green
    
} catch {
    Write-Host "❌ Error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Response: $($_.Exception.Response)" -ForegroundColor Red
}
