package main

import (
	"context"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/v2/bson"
	"go.mongodb.org/mongo-driver/v2/mongo"
	"go.mongodb.org/mongo-driver/v2/mongo/options"
)

func main() {
	fmt.Println("测试本地MongoDB连接...")
	
	// 测试不同的本地连接方式
	testCases := []struct {
		name string
		uri  string
	}{
		{"默认本地连接", "mongodb://localhost:27017"},
		{"127.0.0.1连接", "mongodb://127.0.0.1:27017"},
		{"指定数据库", "mongodb://localhost:27017/demoDb"},
		{"无认证admin", "mongodb://localhost:27017/admin"},
	}
	
	for _, tc := range testCases {
		fmt.Printf("\n测试: %s\n", tc.name)
		fmt.Printf("URI: %s\n", tc.uri)
		
		if testConnection(tc.uri) {
			fmt.Println("✅ 连接成功!")
			
			// 如果连接成功，尝试一些操作
			if tc.name == "默认本地连接" {
				testOperations(tc.uri)
			}
		} else {
			fmt.Println("❌ 连接失败")
		}
	}
	
	fmt.Println("\n=== 建议 ===")
	fmt.Println("如果本地MongoDB连接成功，可以:")
	fmt.Println("1. 修改.env文件使用本地MongoDB:")
	fmt.Println("   MONGODB_HOST=localhost")
	fmt.Println("   MONGODB_PORT=27017")
	fmt.Println("   MONGODB_USERNAME=")
	fmt.Println("   MONGODB_PASSWORD=")
	fmt.Println("")
	fmt.Println("2. 或者检查远程MongoDB服务器:")
	fmt.Println("   - MongoDB服务是否运行")
	fmt.Println("   - 网络连接是否正常")
	fmt.Println("   - 防火墙设置")
	fmt.Println("   - 认证配置")
}

func testConnection(uri string) bool {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	
	clientOptions := options.Client().ApplyURI(uri)
	clientOptions.SetConnectTimeout(3 * time.Second)
	clientOptions.SetServerSelectionTimeout(3 * time.Second)
	
	client, err := mongo.Connect(clientOptions)
	if err != nil {
		fmt.Printf("  连接创建失败: %v\n", err)
		return false
	}
	defer client.Disconnect(ctx)
	
	if err := client.Ping(ctx, nil); err != nil {
		fmt.Printf("  Ping失败: %v\n", err)
		return false
	}
	
	return true
}

func testOperations(uri string) {
	fmt.Println("\n执行基本操作测试...")
	
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	
	clientOptions := options.Client().ApplyURI(uri)
	client, err := mongo.Connect(clientOptions)
	if err != nil {
		fmt.Printf("  操作测试失败: %v\n", err)
		return
	}
	defer client.Disconnect(ctx)
	
	// 列出数据库
	databases, err := client.ListDatabaseNames(ctx, bson.D{})
	if err != nil {
		fmt.Printf("  列出数据库失败: %v\n", err)
	} else {
		fmt.Printf("  可用数据库: %v\n", databases)
	}
	
	// 测试demoDb数据库
	db := client.Database("demoDb")
	collection := db.Collection("test")
	
	// 插入测试文档
	testDoc := bson.D{
		{Key: "test", Value: true},
		{Key: "timestamp", Value: time.Now()},
		{Key: "message", Value: "本地MongoDB连接测试"},
	}
	
	result, err := collection.InsertOne(ctx, testDoc)
	if err != nil {
		fmt.Printf("  插入文档失败: %v\n", err)
	} else {
		fmt.Printf("  ✅ 文档插入成功, ID: %v\n", result.InsertedID)
		
		// 清理测试文档
		_, err = collection.DeleteOne(ctx, bson.D{{Key: "test", Value: true}})
		if err != nil {
			fmt.Printf("  清理文档失败: %v\n", err)
		} else {
			fmt.Printf("  ✅ 测试文档已清理\n")
		}
	}
}
