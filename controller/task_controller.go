package controller

import (
	"context"
	"demo/request"
	"demo/response"
	"demo/router"
	"demo/service"
	"demo/utils"

	"github.com/gin-gonic/gin"
)

// TaskController 任务控制器 - 只处理任务相关的业务逻辑
type TaskController struct {
	taskService *service.TaskService
}

var taskController = &TaskController{
	taskService: service.NewTaskService(),
}

// ==================== 任务查询 ====================
type TaskQueryRequest struct {
	Name__like    string `form:"name" validate:"omitempty,min=1,max=100"`
	Content__like string `form:"content" validate:"omitempty,max=1000"`
	Page          int    `form:"page" validate:"omitempty,min=1"`
	Size          int    `form:"size" validate:"omitempty,min=1,max=100"`
}

func init() {
	router.Api.GET("/task/query", taskController.QueryTasks)
}

func (tc *TaskController) QueryTasks(c *gin.Context) {
	var req TaskQueryRequest

	if err := utils.BindAndValidate(c, &req, "query"); err != nil {
		return
	}

	// 转换为 map 传递给 service
	queryParams := utils.StructToMap(req)

	result, err := tc.taskService.GetTaskList(context.Background(), queryParams)
	if err != nil {
		resp := response.InternalError("查询任务失败: " + err.Error())
		c.JSON(resp.GetHTTPStatus(), resp)
		return
	}

	resp := response.Success("查询任务成功", result)
	c.JSON(resp.GetHTTPStatus(), resp)
}

// ==================== 任务创建 ====================
type CreateTaskRequest struct {
	Name    string `json:"name" binding:"required" validate:"min=1,max=100"`
	Content string `json:"content" validate:"max=1000"`
}

func init() {
	router.Api.POST("/task", taskController.CreateTask)
}

func (tc *TaskController) CreateTask(c *gin.Context) {
	var req CreateTaskRequest

	if err := utils.BindAndValidate(c, &req, "json"); err != nil {
		return
	}

	// 转换为 service 层的请求结构
	serviceReq := &service.CreateTaskRequest{
		Name:    req.Name,
		Content: req.Content,
	}

	task, err := tc.taskService.CreateTask(context.Background(), serviceReq)
	if err != nil {
		resp := response.BadRequest("创建任务失败: " + err.Error())
		c.JSON(resp.GetHTTPStatus(), resp)
		return
	}

	resp := response.Success("创建任务成功", task)
	c.JSON(resp.GetHTTPStatus(), resp)
}

// ==================== 任务详情 ====================
type TaskDetailRequest struct {
	ID string `uri:"id" binding:"required"`
}

func init() {
	router.Api.GET("/task/:id", taskController.GetTaskDetail)
}

func (tc *TaskController) GetTaskDetail(c *gin.Context) {
	var req TaskDetailRequest

	if err := utils.BindAndValidate(c, &req, "uri"); err != nil {
		return
	}

	task, err := tc.taskService.GetTaskByID(context.Background(), req.ID)
	if err != nil {
		resp := response.NotFound("任务不存在: " + err.Error())
		c.JSON(resp.GetHTTPStatus(), resp)
		return
	}

	resp := response.Success("获取任务详情成功", task)
	c.JSON(resp.GetHTTPStatus(), resp)
}

// ==================== 任务更新 ====================
type UpdateTaskRequest struct {
	ID      string `uri:"id" binding:"required"`
	Name    string `json:"name" validate:"min=1,max=100"`
	Content string `json:"content" validate:"max=1000"`
}

func init() {
	router.Api.PUT("/task/:id", taskController.UpdateTask)
}

func (tc *TaskController) UpdateTask(c *gin.Context) {
	var req UpdateTaskRequest

	if err := utils.BindAndValidate(c, &req, "both"); err != nil {
		return
	}

	// 转换为 service 层的请求结构
	serviceReq := &service.UpdateTaskRequest{
		Name:    req.Name,
		Content: req.Content,
	}

	task, err := tc.taskService.UpdateTask(context.Background(), req.ID, serviceReq)
	if err != nil {
		resp := response.BadRequest("更新任务失败: " + err.Error())
		c.JSON(resp.GetHTTPStatus(), resp)
		return
	}

	resp := response.Success("更新任务成功", task)
	c.JSON(resp.GetHTTPStatus(), resp)
}

// ==================== 任务删除 ====================
type DeleteTaskRequest struct {
	ID string `uri:"id" binding:"required"`
}

func init() {
	router.Api.DELETE("/task/:id", taskController.DeleteTask)
}

func (tc *TaskController) DeleteTask(c *gin.Context) {
	var req DeleteTaskRequest

	if err := utils.BindAndValidate(c, &req, "uri"); err != nil {
		return
	}

	err := tc.taskService.DeleteTask(context.Background(), req.ID)
	if err != nil {
		resp := response.BadRequest("删除任务失败: " + err.Error())
		c.JSON(resp.GetHTTPStatus(), resp)
		return
	}

	resp := response.Success("删除任务成功", nil)
	c.JSON(resp.GetHTTPStatus(), resp)
}
