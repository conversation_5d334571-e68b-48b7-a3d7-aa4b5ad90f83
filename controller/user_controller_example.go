package controller

import (
	"context"
	"demo/response"
	"demo/router"
	"demo/service"
	"demo/utils"
	"net/http"

	"github.com/gin-gonic/gin"
)

// UserControllerExample 用户控制器示例 - 展示正确的分层
type UserControllerExample struct {
	userService *service.UserService // 依赖注入业务逻辑层
}

// NewUserControllerExample 创建用户控制器
func NewUserControllerExample() *UserControllerExample {
	return &UserControllerExample{
		userService: service.NewUserService(),
	}
}

// ==================== Controller 层的职责 ====================
// 1. HTTP 请求处理（路径参数、查询参数、请求体）
// 2. 参数绑定和基础验证
// 3. 调用 Service 层
// 4. HTTP 响应处理（状态码、响应格式）
// 5. 错误处理和响应

// CreateUser 创建用户
// @route POST /users
func (uc *UserControllerExample) CreateUser(c *gin.Context) {
	var req service.CreateUserRequest
	
	// ✅ Controller层职责：HTTP参数绑定和基础验证
	if err := utils.BindAndValidate(c, &req, "json"); err != nil {
		return // utils.BindAndValidate 已经处理了错误响应
	}

	// ✅ Controller层职责：调用业务逻辑层
	user, err := uc.userService.CreateUser(context.Background(), &req)
	if err != nil {
		// ✅ Controller层职责：HTTP错误响应处理
		resp := response.Fail(err.Error())
		c.JSON(resp.GetHTTPStatus(), resp)
		return
	}

	// ✅ Controller层职责：HTTP成功响应处理
	resp := response.SuccessWithMessage(user, "用户创建成功")
	c.JSON(resp.GetHTTPStatus(), resp)
}

// GetUser 获取单个用户
// @route GET /users/:id
func (uc *UserControllerExample) GetUser(c *gin.Context) {
	// ✅ Controller层职责：获取HTTP路径参数
	userID := c.Param("id")
	if userID == "" {
		resp := response.Fail("用户ID不能为空")
		c.JSON(resp.GetHTTPStatus(), resp)
		return
	}

	// ✅ Controller层职责：调用业务逻辑层
	user, err := uc.userService.GetUserByID(context.Background(), userID)
	if err != nil {
		// ✅ Controller层职责：根据业务错误返回合适的HTTP状态码
		resp := response.NotFound(err.Error())
		c.JSON(resp.GetHTTPStatus(), resp)
		return
	}

	// ✅ Controller层职责：HTTP成功响应处理
	resp := response.SuccessWithMessage(user, "获取用户成功")
	c.JSON(resp.GetHTTPStatus(), resp)
}

// UpdateUser 更新用户
// @route PUT /users/:id
func (uc *UserControllerExample) UpdateUser(c *gin.Context) {
	// ✅ Controller层职责：获取HTTP路径参数
	userID := c.Param("id")
	if userID == "" {
		resp := response.Fail("用户ID不能为空")
		c.JSON(resp.GetHTTPStatus(), resp)
		return
	}

	// ✅ Controller层职责：HTTP参数绑定和基础验证
	var req service.UpdateUserRequest
	if err := utils.BindAndValidate(c, &req, "json"); err != nil {
		return
	}

	// ✅ Controller层职责：调用业务逻辑层
	user, err := uc.userService.UpdateUser(context.Background(), userID, &req)
	if err != nil {
		resp := response.Fail(err.Error())
		c.JSON(resp.GetHTTPStatus(), resp)
		return
	}

	// ✅ Controller层职责：HTTP成功响应处理
	resp := response.SuccessWithMessage(user, "用户更新成功")
	c.JSON(resp.GetHTTPStatus(), resp)
}

// GetUsers 获取用户列表
// @route GET /users
func (uc *UserControllerExample) GetUsers(c *gin.Context) {
	// ✅ Controller层职责：定义HTTP查询参数结构
	type UserQueryRequest struct {
		Page     int    `form:"page"`
		Size     int    `form:"size"`
		Order    string `form:"order"`
		Username string `form:"username"`
		Email    string `form:"email"`
		Status   string `form:"status"`
	}

	var req UserQueryRequest
	
	// ✅ Controller层职责：HTTP参数绑定和基础验证
	if err := utils.BindAndValidate(c, &req, "query"); err != nil {
		return
	}

	// ✅ Controller层职责：转换HTTP参数为业务层需要的格式
	queryParams := map[string]interface{}{
		"page":     req.Page,
		"size":     req.Size,
		"order":    req.Order,
		"username": req.Username,
		"email":    req.Email,
		"status":   req.Status,
	}
	
	// ✅ Controller层职责：调用业务逻辑层
	result, err := uc.userService.GetUserList(context.Background(), queryParams)
	if err != nil {
		resp := response.InternalError("查询用户失败: " + err.Error())
		c.JSON(resp.GetHTTPStatus(), resp)
		return
	}

	// ✅ Controller层职责：HTTP成功响应处理
	c.JSON(http.StatusOK, result) // 分页结果直接返回
}

// ChangePassword 修改密码
// @route POST /users/:id/change-password
func (uc *UserControllerExample) ChangePassword(c *gin.Context) {
	// ✅ Controller层职责：获取HTTP路径参数
	userID := c.Param("id")
	if userID == "" {
		resp := response.Fail("用户ID不能为空")
		c.JSON(resp.GetHTTPStatus(), resp)
		return
	}

	// ✅ Controller层职责：HTTP参数绑定和基础验证
	var req service.ChangePasswordRequest
	if err := utils.BindAndValidate(c, &req, "json"); err != nil {
		return
	}

	// ✅ Controller层职责：调用业务逻辑层
	err := uc.userService.ChangePassword(context.Background(), userID, &req)
	if err != nil {
		resp := response.Fail(err.Error())
		c.JSON(resp.GetHTTPStatus(), resp)
		return
	}

	// ✅ Controller层职责：HTTP成功响应处理
	resp := response.SuccessWithMessage(nil, "密码修改成功")
	c.JSON(resp.GetHTTPStatus(), resp)
}

// ==================== Controller 层不应该包含的内容 ====================
// ❌ 不应该有：业务验证逻辑（邮箱格式、密码强度等）
// ❌ 不应该有：数据库操作
// ❌ 不应该有：密码加密/解密
// ❌ 不应该有：复杂的数据处理逻辑
// ❌ 不应该有：业务规则判断
// ❌ 不应该有：数据转换逻辑（除了HTTP参数转换）

// 注册路由
func init() {
	controller := NewUserControllerExample()
	
	router.RegisterRoutes([]router.Route{
		{
			Method:  "POST",
			Path:    "/users",
			Handler: controller.CreateUser,
		},
		{
			Method:  "GET",
			Path:    "/users/:id",
			Handler: controller.GetUser,
		},
		{
			Method:  "PUT",
			Path:    "/users/:id",
			Handler: controller.UpdateUser,
		},
		{
			Method:  "GET",
			Path:    "/users",
			Handler: controller.GetUsers,
		},
		{
			Method:  "POST",
			Path:    "/users/:id/change-password",
			Handler: controller.ChangePassword,
		},
	})
}
