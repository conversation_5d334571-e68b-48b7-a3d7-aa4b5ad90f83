package controller

import (
	"context"
	"demo/response"
	"demo/router"
	"demo/service"
	"demo/utils"

	"github.com/gin-gonic/gin"
)

// UserControllerExample 用户控制器示例 - Spring Boot 风格
type UserControllerExample struct {
	userService *service.UserService
}

var userControllerExample = UserControllerExample{
	userService: service.NewUserService(),
}

// ==================== 用户创建 ====================
type CreateUserRequest struct {
	Username string `json:"username" validate:"required,min=3,max=50"`
	Email    string `json:"email" validate:"required,email"`
	Password string `json:"password" validate:"required,min=8"`
}

func init() {
	router.Api.POST("/user/create", userControllerExample.CreateUser)
}

// CreateUser 创建用户 - Spring Boot 风格
func (uc *UserControllerExample) CreateUser(c *gin.Context) {
	var req CreateUserRequest

	// ✅ Controller层：HTTP参数绑定和基础验证
	if err := utils.BindAndValidate(c, &req, "json"); err != nil {
		return
	}

	// ✅ Controller层：调用业务逻辑层
	serviceReq := &service.CreateUserRequest{
		Username: req.Username,
		Email:    req.Email,
		Password: req.Password,
	}

	user, err := uc.userService.CreateUser(context.Background(), serviceReq)
	if err != nil {
		resp := response.Fail(err.Error())
		c.JSON(resp.GetHTTPStatus(), resp)
		return
	}

	resp := response.SuccessWithMessage(user, "用户创建成功")
	c.JSON(resp.GetHTTPStatus(), resp)
}

// ==================== 用户查询 ====================
type UserQueryRequest struct {
	Username__like string `form:"username" validate:"omitempty,min=1,max=50"`
	Email__like    string `form:"email" validate:"omitempty,email"`
	Status__eq     string `form:"status" validate:"omitempty,oneof=active inactive"`
	Page           int    `form:"page" validate:"omitempty,min=1"`
	Size           int    `form:"size" validate:"omitempty,min=1,max=100"`
}

func init() {
	router.Api.GET("/user/query", userControllerExample.QueryUsers)
}

// QueryUsers 查询用户列表 - Spring Boot 风格
func (uc *UserControllerExample) QueryUsers(c *gin.Context) {
	var req UserQueryRequest

	if err := utils.BindAndValidate(c, &req, "query"); err != nil {
		return
	}

	// ✅ Controller层：转换参数格式
	queryParams := map[string]interface{}{
		"username": req.Username__like,
		"email":    req.Email__like,
		"status":   req.Status__eq,
		"page":     req.Page,
		"size":     req.Size,
	}

	result, err := uc.userService.GetUserList(context.Background(), queryParams)
	if err != nil {
		resp := response.InternalError("查询用户失败: " + err.Error())
		c.JSON(resp.GetHTTPStatus(), resp)
		return
	}

	resp := response.SuccessWithMessage(result, "查询用户成功")
	c.JSON(resp.GetHTTPStatus(), resp)
}

// ==================== 用户详情 ====================
func init() {
	router.Api.GET("/user/:id", userControllerExample.GetUserByID)
}

// GetUserByID 根据ID获取用户 - Spring Boot 风格
func (uc *UserControllerExample) GetUserByID(c *gin.Context) {
	userID := c.Param("id")
	if userID == "" {
		resp := response.Fail("用户ID不能为空")
		c.JSON(resp.GetHTTPStatus(), resp)
		return
	}

	user, err := uc.userService.GetUserByID(context.Background(), userID)
	if err != nil {
		resp := response.NotFound(err.Error())
		c.JSON(resp.GetHTTPStatus(), resp)
		return
	}

	resp := response.SuccessWithMessage(user, "获取用户成功")
	c.JSON(resp.GetHTTPStatus(), resp)
}

// ==================== 用户更新 ====================
type UpdateUserRequest struct {
	Username string `json:"username" validate:"omitempty,min=3,max=50"`
	Email    string `json:"email" validate:"omitempty,email"`
	Status   string `json:"status" validate:"omitempty,oneof=active inactive"`
}

func init() {
	router.Api.PUT("/user/:id", userControllerExample.UpdateUser)
}

// UpdateUser 更新用户 - Spring Boot 风格
func (uc *UserControllerExample) UpdateUser(c *gin.Context) {
	userID := c.Param("id")
	if userID == "" {
		resp := response.Fail("用户ID不能为空")
		c.JSON(resp.GetHTTPStatus(), resp)
		return
	}

	var req UpdateUserRequest
	if err := utils.BindAndValidate(c, &req, "json"); err != nil {
		return
	}

	serviceReq := &service.UpdateUserRequest{
		Username: req.Username,
		Email:    req.Email,
		Status:   req.Status,
	}

	user, err := uc.userService.UpdateUser(context.Background(), userID, serviceReq)
	if err != nil {
		resp := response.Fail(err.Error())
		c.JSON(resp.GetHTTPStatus(), resp)
		return
	}

	resp := response.SuccessWithMessage(user, "用户更新成功")
	c.JSON(resp.GetHTTPStatus(), resp)
}

// ==================== 修改密码 ====================
type ChangePasswordRequest struct {
	OldPassword string `json:"old_password" validate:"required"`
	NewPassword string `json:"new_password" validate:"required,min=8"`
}

func init() {
	router.Api.POST("/user/:id/change-password", userControllerExample.ChangePassword)
}

// ChangePassword 修改密码 - Spring Boot 风格
func (uc *UserControllerExample) ChangePassword(c *gin.Context) {
	userID := c.Param("id")
	if userID == "" {
		resp := response.Fail("用户ID不能为空")
		c.JSON(resp.GetHTTPStatus(), resp)
		return
	}

	var req ChangePasswordRequest
	if err := utils.BindAndValidate(c, &req, "json"); err != nil {
		return
	}

	serviceReq := &service.ChangePasswordRequest{
		OldPassword: req.OldPassword,
		NewPassword: req.NewPassword,
	}

	err := uc.userService.ChangePassword(context.Background(), userID, serviceReq)
	if err != nil {
		resp := response.Fail(err.Error())
		c.JSON(resp.GetHTTPStatus(), resp)
		return
	}

	resp := response.SuccessWithMessage(nil, "密码修改成功")
	c.JSON(resp.GetHTTPStatus(), resp)
}
