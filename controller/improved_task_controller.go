package controller

import (
	"context"
	"demo/database"
	"demo/model"
	"demo/response"
	"demo/router"
	"demo/validator"
	"net/http"

	"github.com/gin-gonic/gin"
)

// ImprovedTaskController 改进的任务控制器 - 保持你原有的设计风格
type ImprovedTaskController struct{}

var improvedTaskController = ImprovedTaskController{}

// ==================== 查询任务 ====================
type taskQueryParam struct {
	Name__like string `form:"name" validate:"omitempty,min=1,max=100"`
	Content    string `form:"content" validate:"omitempty,min=1,max=500"`
	Page       int    `form:"page" validate:"omitempty,min=1"`
	Size       int    `form:"size" validate:"omitempty,min=1,max=100"`
	Order      string `form:"order" validate:"omitempty"`
}

// 保持你原来的路由注册方式
func init() {
	router.Api.GET("/task/query", improvedTaskController.GetTaskList)
}

func (tc *ImprovedTaskController) GetTaskList(c *gin.Context) {
	var param taskQueryParam
	
	// 使用改进的绑定和验证，但保持简洁
	if err := bindAndValidate(c, &param, "query"); err != nil {
		return
	}

	// 使用优化后的分页服务，但保持你原来的 StructToBsonM 便利性
	paginationService := database.NewPaginationService[model.Task]("task")
	queryParams := database.StructToBsonM(param)
	
	pageResponse, err := paginationService.QueryWithPagination(context.Background(), queryParams)
	if err != nil {
		resp := response.Fail("查询失败: " + err.Error())
		c.JSON(http.StatusInternalServerError, resp)
		return
	}

	c.JSON(http.StatusOK, pageResponse)
}

// ==================== 创建任务 ====================
type taskCreateParam struct {
	Name    string `json:"name" validate:"required,min=1,max=100"`
	Content string `json:"content" validate:"required,min=1,max=1000"`
}

func init() {
	router.Api.POST("/task/create", improvedTaskController.CreateTask)
}

func (tc *ImprovedTaskController) CreateTask(c *gin.Context) {
	var param taskCreateParam
	
	if err := bindAndValidate(c, &param, "json"); err != nil {
		return
	}

	task := model.Task{
		Name:    param.Name,
		Content: param.Content,
	}

	// 使用优化后的数据库连接
	collection := database.GetCollection[model.Task]("task")
	result, err := collection.Creator().InsertOne(context.Background(), &task)
	if err != nil {
		resp := response.Fail("创建失败: " + err.Error())
		c.JSON(http.StatusInternalServerError, resp)
		return
	}

	resp := response.SuccessWithMessage(result, "任务创建成功")
	c.JSON(http.StatusOK, resp)
}

// ==================== 更新任务 ====================
type taskUpdateParam struct {
	ID      string `json:"id" validate:"required,objectid"`
	Name    string `json:"name" validate:"omitempty,min=1,max=100"`
	Content string `json:"content" validate:"omitempty,min=1,max=1000"`
}

func init() {
	router.Api.PUT("/task/update", improvedTaskController.UpdateTask)
}

func (tc *ImprovedTaskController) UpdateTask(c *gin.Context) {
	var param taskUpdateParam
	
	if err := bindAndValidate(c, &param, "json"); err != nil {
		return
	}

	// 使用你原来的便利方法
	updateDoc := database.StructToBsonM(param)
	delete(updateDoc, "_id") // 移除ID字段

	collection := database.GetCollection[model.Task]("task")
	
	// 使用优化后的查询构建器
	queryBuilder := database.NewQueryBuilder()
	filter := queryBuilder.BuildFromMap(map[string]interface{}{
		"id": param.ID,
	})

	result, err := collection.Updater().Filter(filter.Build()).Updates(updateDoc).UpdateOne(context.Background())
	if err != nil {
		resp := response.Fail("更新失败: " + err.Error())
		c.JSON(http.StatusInternalServerError, resp)
		return
	}

	if result.MatchedCount == 0 {
		resp := response.NotFound("任务不存在")
		c.JSON(http.StatusNotFound, resp)
		return
	}

	resp := response.SuccessWithMessage(nil, "任务更新成功")
	c.JSON(http.StatusOK, resp)
}

// ==================== 删除任务 ====================
type taskDeleteParam struct {
	ID string `json:"id" validate:"required,objectid"`
}

func init() {
	router.Api.DELETE("/task/delete", improvedTaskController.DeleteTask)
}

func (tc *ImprovedTaskController) DeleteTask(c *gin.Context) {
	var param taskDeleteParam
	
	if err := bindAndValidate(c, &param, "json"); err != nil {
		return
	}

	collection := database.GetCollection[model.Task]("task")
	
	queryBuilder := database.NewQueryBuilder()
	filter := queryBuilder.BuildFromMap(map[string]interface{}{
		"id": param.ID,
	})

	result, err := collection.Deleter().Filter(filter.Build()).DeleteOne(context.Background())
	if err != nil {
		resp := response.Fail("删除失败: " + err.Error())
		c.JSON(http.StatusInternalServerError, resp)
		return
	}

	if result.DeletedCount == 0 {
		resp := response.NotFound("任务不存在")
		c.JSON(http.StatusNotFound, resp)
		return
	}

	resp := response.SuccessWithMessage(nil, "任务删除成功")
	c.JSON(http.StatusOK, resp)
}

// ==================== 获取单个任务 ====================
type taskGetParam struct {
	ID string `form:"id" validate:"required,objectid"`
}

func init() {
	router.Api.GET("/task/get", improvedTaskController.GetTask)
}

func (tc *ImprovedTaskController) GetTask(c *gin.Context) {
	var param taskGetParam
	
	if err := bindAndValidate(c, &param, "query"); err != nil {
		return
	}

	collection := database.GetCollection[model.Task]("task")
	
	queryBuilder := database.NewQueryBuilder()
	filter := queryBuilder.BuildFromMap(map[string]interface{}{
		"id": param.ID,
	})

	result, err := collection.Finder().Filter(filter.Build()).FindOne(context.Background())
	if err != nil {
		resp := response.NotFound("任务不存在")
		c.JSON(http.StatusNotFound, resp)
		return
	}

	resp := response.Success(result)
	c.JSON(http.StatusOK, resp)
}

// ==================== 辅助函数 ====================
// 保持简洁的绑定和验证函数
func bindAndValidate(c *gin.Context, obj interface{}, bindType string) error {
	var err error
	
	switch bindType {
	case "json":
		err = c.ShouldBindJSON(obj)
	case "query":
		err = c.ShouldBindQuery(obj)
	case "form":
		err = c.ShouldBind(obj)
	}
	
	if err != nil {
		resp := response.Fail("参数绑定失败: " + err.Error())
		c.JSON(http.StatusBadRequest, resp)
		return err
	}

	if err := validator.Validate(obj); err != nil {
		validationResp := validator.NewValidationErrorResponse(err)
		c.JSON(http.StatusBadRequest, validationResp)
		return err
	}

	return nil
}
