package controller

import (
	"demo/response"
	"demo/service"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

// BaseController 基础控制器
type BaseController[T any] struct {
	service service.SimpleService[T]
}

// NewBaseController 创建基础控制器
func NewBaseController[T any](svc service.SimpleService[T]) *BaseController[T] {
	return &BaseController[T]{
		service: svc,
	}
}

// Create 创建资源
func (bc *BaseController[T]) Create(c *gin.Context) {
	var entity T
	if err := c.ShouldBindJSON(&entity); err != nil {
		resp := response.Fail("Invalid request body: " + err.Error())
		c.<PERSON>(http.StatusBadRequest, resp)
		return
	}

	result, err := bc.service.Create(c.Request.Context(), &entity)
	if err != nil {
		resp := response.Fail("Failed to create resource: " + err.Error())
		c.<PERSON>(http.StatusInternalServerError, resp)
		return
	}

	resp := response.SuccessWithMessage(result, "Resource created successfully")
	c.<PERSON>(http.StatusCreated, resp)
}

// GetByID 根据ID获取资源
func (bc *BaseController[T]) GetByID(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		resp := response.Fail("ID parameter is required")
		c.JSON(http.StatusBadRequest, resp)
		return
	}

	result, err := bc.service.GetByID(c.Request.Context(), id)
	if err != nil {
		resp := response.NotFound("Resource not found")
		c.JSON(http.StatusNotFound, resp)
		return
	}

	resp := response.Success(result)
	c.JSON(http.StatusOK, resp)
}

// Update 更新资源
func (bc *BaseController[T]) Update(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		resp := response.Fail("ID parameter is required")
		c.JSON(http.StatusBadRequest, resp)
		return
	}

	var entity T
	if err := c.ShouldBindJSON(&entity); err != nil {
		resp := response.Fail("Invalid request body: " + err.Error())
		c.JSON(http.StatusBadRequest, resp)
		return
	}

	result, err := bc.service.Update(c.Request.Context(), id, &entity)
	if err != nil {
		if err.Error() == "entity not found" {
			resp := response.NotFound("Resource not found")
			c.JSON(http.StatusNotFound, resp)
		} else {
			resp := response.Fail("Failed to update resource: " + err.Error())
			c.JSON(http.StatusInternalServerError, resp)
		}
		return
	}

	resp := response.SuccessWithMessage(result, "Resource updated successfully")
	c.JSON(http.StatusOK, resp)
}

// Delete 删除资源
func (bc *BaseController[T]) Delete(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		resp := response.Fail("ID parameter is required")
		c.JSON(http.StatusBadRequest, resp)
		return
	}

	err := bc.service.Delete(c.Request.Context(), id)
	if err != nil {
		if err.Error() == "entity not found" {
			resp := response.NotFound("Resource not found")
			c.JSON(http.StatusNotFound, resp)
		} else {
			resp := response.Fail("Failed to delete resource: " + err.Error())
			c.JSON(http.StatusInternalServerError, resp)
		}
		return
	}

	resp := response.SuccessWithMessage(nil, "Resource deleted successfully")
	c.JSON(http.StatusOK, resp)
}

// List 分页查询资源
func (bc *BaseController[T]) List(c *gin.Context) {
	// 解析查询参数
	params := bc.parseQueryParams(c)

	result, err := bc.service.List(c.Request.Context(), params)
	if err != nil {
		resp := response.Fail("Failed to query resources: " + err.Error())
		c.JSON(http.StatusInternalServerError, resp)
		return
	}

	c.JSON(http.StatusOK, result)
}

// ListAll 查询所有资源
func (bc *BaseController[T]) ListAll(c *gin.Context) {
	// 解析查询参数
	params := bc.parseQueryParams(c)

	result, err := bc.service.ListAll(c.Request.Context(), params)
	if err != nil {
		resp := response.Fail("Failed to query resources: " + err.Error())
		c.JSON(http.StatusInternalServerError, resp)
		return
	}

	resp := response.Success(result)
	c.JSON(http.StatusOK, resp)
}

// parseQueryParams 解析查询参数
func (bc *BaseController[T]) parseQueryParams(c *gin.Context) map[string]interface{} {
	params := make(map[string]interface{})

	// 解析所有查询参数
	for key, values := range c.Request.URL.Query() {
		if len(values) > 0 {
			value := values[0]
			
			// 尝试转换为数字
			if intValue, err := strconv.Atoi(value); err == nil {
				params[key] = intValue
			} else {
				params[key] = value
			}
		}
	}

	return params
}

// BindAndValidate 绑定并验证请求体
func (bc *BaseController[T]) BindAndValidate(c *gin.Context, obj interface{}) error {
	if err := c.ShouldBindJSON(obj); err != nil {
		return err
	}
	
	// 这里可以添加自定义验证逻辑
	return nil
}

// GetRequestID 获取请求ID
func (bc *BaseController[T]) GetRequestID(c *gin.Context) string {
	if requestID, exists := c.Get("request_id"); exists {
		return requestID.(string)
	}
	return ""
}

// RespondWithError 统一错误响应
func (bc *BaseController[T]) RespondWithError(c *gin.Context, statusCode int, message string) {
	var resp response.StandardResponse
	
	switch statusCode {
	case http.StatusBadRequest:
		resp = response.Fail(message)
	case http.StatusNotFound:
		resp = response.NotFound(message)
	case http.StatusUnauthorized:
		resp = response.Unauthorized(message)
	default:
		resp = response.InternalError(message)
	}
	
	// 添加请求ID
	if requestID := bc.GetRequestID(c); requestID != "" {
		resp = response.NewResponseBuilder().
			Error(response.ResponseCode(statusCode), message).
			WithRequestID(requestID).
			Build()
	}
	
	c.JSON(statusCode, resp)
}

// RespondWithSuccess 统一成功响应
func (bc *BaseController[T]) RespondWithSuccess(c *gin.Context, data interface{}, message string) {
	resp := response.SuccessWithMessage(data, message)
	
	// 添加请求ID
	if requestID := bc.GetRequestID(c); requestID != "" {
		resp = response.NewResponseBuilder().
			SuccessWithMessage(data, message).
			WithRequestID(requestID).
			Build()
	}
	
	c.JSON(http.StatusOK, resp)
}
