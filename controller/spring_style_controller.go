package controller

import (
	"context"
	"demo/database"
	"demo/model"
	"demo/response"
	"demo/router"
	"demo/validator"
	"net/http"

	"github.com/gin-gonic/gin"
)

// SpringStyleTaskController Spring Boot风格的任务控制器
// 保持你原来的设计优点，同时集成新的优化功能
type SpringStyleTaskController struct{}

var springTaskController = SpringStyleTaskController{}

// ==================== 查询任务列表 ====================
// @GetMapping("/task/query")
// @Description: 分页查询任务列表，支持多种查询条件
type TaskQueryRequest struct {
	// 基础查询字段
	Name__like string `form:"name" validate:"omitempty,min=1,max=100" description:"任务名称模糊查询"`
	Content    string `form:"content" validate:"omitempty,min=1,max=500" description:"任务内容查询"`
	
	// 高级查询字段
	Name__eq     string `form:"name__eq" validate:"omitempty,min=1,max=100" description:"任务名称精确查询"`
	CreatedAt__dgt string `form:"created_at__dgt" validate:"omitempty" description:"创建时间大于"`
	CreatedAt__dlt string `form:"created_at__dlt" validate:"omitempty" description:"创建时间小于"`
	
	// 分页字段
	Page  int    `form:"page" validate:"omitempty,min=1" description:"页码"`
	Size  int    `form:"size" validate:"omitempty,min=1,max=100" description:"每页大小"`
	Order string `form:"order" validate:"omitempty" description:"排序字段"`
}

func init() {
	router.Api.GET("/task/query", springTaskController.QueryTasks)
}

func (tc *SpringStyleTaskController) QueryTasks(c *gin.Context) {
	var req TaskQueryRequest
	
	// 参数绑定和验证 - 保持简洁
	if err := bindAndValidate(c, &req, "query"); err != nil {
		return
	}

	// 使用优化后的查询服务，但保持原来的便利性
	result, err := queryWithPagination[model.Task]("task", req)
	if err != nil {
		respondError(c, "查询任务失败", err)
		return
	}

	c.JSON(http.StatusOK, result)
}

// ==================== 创建任务 ====================
// @PostMapping("/task/create")
// @Description: 创建新任务
type TaskCreateRequest struct {
	Name    string `json:"name" validate:"required,min=1,max=100" description:"任务名称"`
	Content string `json:"content" validate:"required,min=1,max=1000" description:"任务内容"`
}

func init() {
	router.Api.POST("/task/create", springTaskController.CreateTask)
}

func (tc *SpringStyleTaskController) CreateTask(c *gin.Context) {
	var req TaskCreateRequest
	
	// 参数绑定和验证
	if err := bindAndValidate(c, &req, "json"); err != nil {
		return
	}

	// 创建任务实体
	task := &model.Task{
		Name:    req.Name,
		Content: req.Content,
	}

	// 使用优化后的数据库操作
	result, err := createEntity("task", task)
	if err != nil {
		respondError(c, "创建任务失败", err)
		return
	}

	respondSuccess(c, result, "任务创建成功")
}

// ==================== 更新任务 ====================
// @PutMapping("/task/update")
// @Description: 更新任务信息
type TaskUpdateRequest struct {
	ID      string `json:"id" validate:"required,objectid" description:"任务ID"`
	Name    string `json:"name" validate:"omitempty,min=1,max=100" description:"任务名称"`
	Content string `json:"content" validate:"omitempty,min=1,max=1000" description:"任务内容"`
}

func init() {
	router.Api.PUT("/task/update", springTaskController.UpdateTask)
}

func (tc *SpringStyleTaskController) UpdateTask(c *gin.Context) {
	var req TaskUpdateRequest
	
	if err := bindAndValidate(c, &req, "json"); err != nil {
		return
	}

	// 使用优化后的更新逻辑
	err := updateEntity("task", req.ID, req)
	if err != nil {
		respondError(c, "更新任务失败", err)
		return
	}

	respondSuccess(c, nil, "任务更新成功")
}

// ==================== 删除任务 ====================
// @DeleteMapping("/task/delete")
// @Description: 删除任务
type TaskDeleteRequest struct {
	ID string `json:"id" validate:"required,objectid" description:"任务ID"`
}

func init() {
	router.Api.DELETE("/task/delete", springTaskController.DeleteTask)
}

func (tc *SpringStyleTaskController) DeleteTask(c *gin.Context) {
	var req TaskDeleteRequest
	
	if err := bindAndValidate(c, &req, "json"); err != nil {
		return
	}

	err := deleteEntity("task", req.ID)
	if err != nil {
		respondError(c, "删除任务失败", err)
		return
	}

	respondSuccess(c, nil, "任务删除成功")
}

// ==================== 获取单个任务 ====================
// @GetMapping("/task/get")
// @Description: 根据ID获取任务详情
type TaskGetRequest struct {
	ID string `form:"id" validate:"required,objectid" description:"任务ID"`
}

func init() {
	router.Api.GET("/task/get", springTaskController.GetTask)
}

func (tc *SpringStyleTaskController) GetTask(c *gin.Context) {
	var req TaskGetRequest
	
	if err := bindAndValidate(c, &req, "query"); err != nil {
		return
	}

	result, err := getEntityByID[model.Task]("task", req.ID)
	if err != nil {
		respondError(c, "获取任务失败", err)
		return
	}

	respondSuccess(c, result, "获取任务成功")
}

// ==================== 通用辅助函数 ====================
// 保持你原来设计的简洁性，同时集成新的功能

// bindAndValidate 统一的参数绑定和验证
func bindAndValidate(c *gin.Context, obj interface{}, bindType string) error {
	var err error
	
	switch bindType {
	case "json":
		err = c.ShouldBindJSON(obj)
	case "query":
		err = c.ShouldBindQuery(obj)
	case "form":
		err = c.ShouldBind(obj)
	}
	
	if err != nil {
		resp := response.Fail("参数绑定失败: " + err.Error())
		c.JSON(http.StatusBadRequest, resp)
		return err
	}

	// 验证参数
	if err := validator.Validate(obj); err != nil {
		validationResp := validator.NewValidationErrorResponse(err)
		c.JSON(http.StatusBadRequest, validationResp)
		return err
	}

	return nil
}

// queryWithPagination 通用分页查询
func queryWithPagination[T any](collectionName string, params interface{}) (*response.PagedResponse, error) {
	paginationService := database.NewPaginationService[T](collectionName)
	queryParams := database.StructToBsonM(params) // 保持原来的便利方法
	return paginationService.QueryWithPagination(context.Background(), queryParams)
}

// createEntity 通用创建实体
func createEntity[T any](collectionName string, entity *T) (*T, error) {
	collection := database.GetCollection[T](collectionName)
	return collection.Creator().InsertOne(context.Background(), entity)
}

// updateEntity 通用更新实体
func updateEntity(collectionName string, id string, updateData interface{}) error {
	collection := database.GetCollection[model.Task](collectionName)
	
	queryBuilder := database.NewQueryBuilder()
	filter := queryBuilder.BuildFromMap(map[string]interface{}{"id": id})
	updateDoc := database.StructToBsonM(updateData)
	delete(updateDoc, "_id")

	result, err := collection.Updater().Filter(filter.Build()).Updates(updateDoc).UpdateOne(context.Background())
	if err != nil {
		return err
	}
	
	if result.MatchedCount == 0 {
		return fmt.Errorf("entity not found")
	}
	
	return nil
}

// deleteEntity 通用删除实体
func deleteEntity(collectionName string, id string) error {
	collection := database.GetCollection[model.Task](collectionName)
	
	queryBuilder := database.NewQueryBuilder()
	filter := queryBuilder.BuildFromMap(map[string]interface{}{"id": id})

	result, err := collection.Deleter().Filter(filter.Build()).DeleteOne(context.Background())
	if err != nil {
		return err
	}
	
	if result.DeletedCount == 0 {
		return fmt.Errorf("entity not found")
	}
	
	return nil
}

// getEntityByID 通用根据ID获取实体
func getEntityByID[T any](collectionName string, id string) (*T, error) {
	collection := database.GetCollection[T](collectionName)
	
	queryBuilder := database.NewQueryBuilder()
	filter := queryBuilder.BuildFromMap(map[string]interface{}{"id": id})

	return collection.Finder().Filter(filter.Build()).FindOne(context.Background())
}

// respondError 统一错误响应
func respondError(c *gin.Context, message string, err error) {
	resp := response.Fail(message + ": " + err.Error())
	c.JSON(http.StatusInternalServerError, resp)
}

// respondSuccess 统一成功响应
func respondSuccess(c *gin.Context, data interface{}, message string) {
	resp := response.SuccessWithMessage(data, message)
	c.JSON(http.StatusOK, resp)
}
