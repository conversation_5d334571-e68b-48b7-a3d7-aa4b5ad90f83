package controller

import (
	"context"
	"demo/model"
	"demo/response"
	"demo/router"
	"demo/service"
	"demo/utils"

	"github.com/gin-gonic/gin"
)

// UserController 用户控制器 - 只处理用户相关的业务逻辑
type UserController struct{}

var userController = UserController{}

// ==================== 用户查询 ====================
type UserQueryRequest struct {
	Username__like string `form:"username" validate:"omitempty,min=1,max=50"`
	Email__like    string `form:"email" validate:"omitempty,email"`
	Status__eq     string `form:"status" validate:"omitempty,oneof=active inactive"`
	Page           int    `form:"page" validate:"omitempty,min=1"`
	Size           int    `form:"size" validate:"omitempty,min=1,max=100"`
}

func init() {
	router.Api.GET("/user/query", userController.QueryUsers)
}

func (uc *UserController) QueryUsers(c *gin.Context) {
	var req UserQueryRequest

	if err := utils.BindAndValidate(c, &req, "query"); err != nil {
		return
	}

	// 使用 service 包的分页服务
	paginationService := service.NewPaginationService[model.User]("user")
	queryParams := utils.StructToMap(req)

	result, err := paginationService.QueryWithPagination(context.Background(), queryParams)
	if err != nil {
		resp := response.InternalError("查询用户失败: " + err.Error())
		c.JSON(resp.GetHTTPStatus(), resp)
		return
	}

	resp := response.SuccessWithMessage(result, "查询用户成功")
	c.JSON(resp.GetHTTPStatus(), resp)
}

// ==================== 用户创建 ====================
type UserCreateRequest struct {
	Username string `json:"username" validate:"required,min=3,max=50"`
	Email    string `json:"email" validate:"required,email"`
	Password string `json:"password" validate:"required,min=6,max=100"`
}

func init() {
	router.Api.POST("/user/create", userController.CreateUser)
}

func (uc *UserController) CreateUser(c *gin.Context) {
	var req UserCreateRequest

	if err := utils.BindAndValidate(c, &req, "json"); err != nil {
		return
	}

	// 用户特有的业务逻辑：密码加密
	hashedPassword, err := uc.hashPassword(req.Password)
	if err != nil {
		resp := response.InternalError("密码加密失败: " + err.Error())
		c.JSON(resp.GetHTTPStatus(), resp)
		return
	}

	user := &model.User{
		Username: req.Username,
		Email:    req.Email,
		Password: hashedPassword,
		Status:   "active",
	}

	result, err := utils.CreateEntity("user", user)
	if err != nil {
		resp := response.InternalError("创建用户失败: " + err.Error())
		c.JSON(resp.GetHTTPStatus(), resp)
		return
	}

	// 不返回密码字段
	result.Password = ""
	resp := response.SuccessWithMessage(result, "用户创建成功")
	c.JSON(resp.GetHTTPStatus(), resp)
}

// ==================== 用户特有的业务方法 ====================

// ChangePassword 修改密码 - 用户特有的业务逻辑
type ChangePasswordRequest struct {
	UserID      string `json:"user_id" validate:"required,objectid"`
	OldPassword string `json:"old_password" validate:"required"`
	NewPassword string `json:"new_password" validate:"required,min=6,max=100"`
}

func init() {
	router.Api.POST("/user/change-password", userController.ChangePassword)
}

func (uc *UserController) ChangePassword(c *gin.Context) {
	var req ChangePasswordRequest

	if err := utils.BindAndValidate(c, &req, "json"); err != nil {
		return
	}

	// 用户特有的业务逻辑：验证旧密码、加密新密码
	user, err := utils.GetEntityByID[model.User]("user", req.UserID)
	if err != nil {
		resp := response.NotFound("用户不存在")
		c.JSON(resp.GetHTTPStatus(), resp)
		return
	}

	// 验证旧密码
	if !uc.verifyPassword(req.OldPassword, user.Password) {
		resp := response.Fail("旧密码错误")
		c.JSON(resp.GetHTTPStatus(), resp)
		return
	}

	// 加密新密码
	hashedNewPassword, err := uc.hashPassword(req.NewPassword)
	if err != nil {
		resp := response.InternalError("新密码加密失败: " + err.Error())
		c.JSON(resp.GetHTTPStatus(), resp)
		return
	}

	// 更新密码
	updateData := map[string]interface{}{
		"password": hashedNewPassword,
	}

	err = utils.UpdateEntity("user", req.UserID, updateData)
	if err != nil {
		resp := response.InternalError("密码更新失败: " + err.Error())
		c.JSON(resp.GetHTTPStatus(), resp)
		return
	}

	resp := response.SuccessWithMessage(nil, "密码修改成功")
	c.JSON(resp.GetHTTPStatus(), resp)
}

// ResetPassword 重置密码 - 用户特有的业务逻辑
type ResetPasswordRequest struct {
	Email string `json:"email" validate:"required,email"`
}

func init() {
	router.Api.POST("/user/reset-password", userController.ResetPassword)
}

func (uc *UserController) ResetPassword(c *gin.Context) {
	var req ResetPasswordRequest

	if err := utils.BindAndValidate(c, &req, "json"); err != nil {
		return
	}

	// 用户特有的业务逻辑：生成重置令牌、发送邮件等
	// 这里简化处理，实际应该生成安全的重置令牌

	// 这里需要实现根据email查找用户的逻辑
	// 简化处理，实际应该有专门的查询方法

	// 生成临时密码（实际应该发送重置链接）
	tempPassword := uc.generateTempPassword()
	hashedTempPassword, err := uc.hashPassword(tempPassword)
	if err != nil {
		resp := response.InternalError("生成临时密码失败: " + err.Error())
		c.JSON(resp.GetHTTPStatus(), resp)
		return
	}

	// 更新用户密码（这里简化了查找逻辑）
	updateData := map[string]interface{}{
		"password": hashedTempPassword,
	}

	// 实际应该先查找用户ID，这里简化处理
	// err = utils.UpdateEntity("user", userID, updateData)

	resp := response.SuccessWithMessage(map[string]string{
		"temp_password": tempPassword,
		"message":       "临时密码已生成，请及时修改",
	}, "密码重置成功")
	c.JSON(resp.GetHTTPStatus(), resp)
}

// ==================== 用户控制器的私有方法 ====================

// hashPassword 密码加密 - 用户控制器特有的方法
func (uc *UserController) hashPassword(password string) (string, error) {
	// 这里应该使用 bcrypt 等安全的加密方法
	// 简化处理，实际项目中应该使用：
	// return bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	return "hashed_" + password, nil
}

// verifyPassword 验证密码 - 用户控制器特有的方法
func (uc *UserController) verifyPassword(password, hashedPassword string) bool {
	// 这里应该使用 bcrypt 验证
	// 简化处理，实际项目中应该使用：
	// return bcrypt.CompareHashAndPassword([]byte(hashedPassword), []byte(password)) == nil
	return hashedPassword == "hashed_"+password
}

// generateTempPassword 生成临时密码 - 用户控制器特有的方法
func (uc *UserController) generateTempPassword() string {
	// 这里应该生成安全的随机密码
	// 简化处理
	return "temp123456"
}
