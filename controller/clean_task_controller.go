package controller

import (
	"demo/model"
	"demo/router"
	"demo/utils"
	"net/http"

	"github.com/gin-gonic/gin"
)

// CleanTaskController 干净的任务控制器 - 职责单一，只处理任务相关的业务逻辑
type CleanTaskController struct{}

var cleanTaskController = CleanTaskController{}

// ==================== 查询任务列表 ====================
// @GetMapping("/task/query")
// @Description: 分页查询任务列表，支持多种查询条件
type TaskQueryRequest struct {
	// 基础查询字段
	Name__like string `form:"name" validate:"omitempty,min=1,max=100" description:"任务名称模糊查询"`
	Content    string `form:"content" validate:"omitempty,min=1,max=500" description:"任务内容查询"`
	
	// 高级查询字段
	Name__eq       string `form:"name__eq" validate:"omitempty,min=1,max=100" description:"任务名称精确查询"`
	CreatedAt__dgt string `form:"created_at__dgt" validate:"omitempty" description:"创建时间大于"`
	CreatedAt__dlt string `form:"created_at__dlt" validate:"omitempty" description:"创建时间小于"`
	
	// 分页字段
	Page  int    `form:"page" validate:"omitempty,min=1" description:"页码"`
	Size  int    `form:"size" validate:"omitempty,min=1,max=100" description:"每页大小"`
	Order string `form:"order" validate:"omitempty" description:"排序字段"`
}

func init() {
	router.Api.GET("/task/query", cleanTaskController.QueryTasks)
}

func (tc *CleanTaskController) QueryTasks(c *gin.Context) {
	var req TaskQueryRequest
	
	// 参数绑定和验证 - 使用通用工具函数
	if err := utils.BindAndValidate(c, &req, "query"); err != nil {
		return
	}

	// 使用通用查询函数
	result, err := utils.QueryWithPagination[model.Task]("task", req)
	if err != nil {
		utils.RespondError(c, "查询任务失败", err)
		return
	}

	c.JSON(http.StatusOK, result)
}

// ==================== 创建任务 ====================
// @PostMapping("/task/create")
// @Description: 创建新任务
type TaskCreateRequest struct {
	Name    string `json:"name" validate:"required,min=1,max=100" description:"任务名称"`
	Content string `json:"content" validate:"required,min=1,max=1000" description:"任务内容"`
}

func init() {
	router.Api.POST("/task/create", cleanTaskController.CreateTask)
}

func (tc *CleanTaskController) CreateTask(c *gin.Context) {
	var req TaskCreateRequest
	
	// 参数绑定和验证
	if err := utils.BindAndValidate(c, &req, "json"); err != nil {
		return
	}

	// 创建任务实体
	task := &model.Task{
		Name:    req.Name,
		Content: req.Content,
	}

	// 使用通用创建函数
	result, err := utils.CreateEntity("task", task)
	if err != nil {
		utils.RespondError(c, "创建任务失败", err)
		return
	}

	utils.RespondSuccess(c, result, "任务创建成功")
}

// ==================== 更新任务 ====================
// @PutMapping("/task/update")
// @Description: 更新任务信息
type TaskUpdateRequest struct {
	ID      string `json:"id" validate:"required,objectid" description:"任务ID"`
	Name    string `json:"name" validate:"omitempty,min=1,max=100" description:"任务名称"`
	Content string `json:"content" validate:"omitempty,min=1,max=1000" description:"任务内容"`
}

func init() {
	router.Api.PUT("/task/update", cleanTaskController.UpdateTask)
}

func (tc *CleanTaskController) UpdateTask(c *gin.Context) {
	var req TaskUpdateRequest
	
	if err := utils.BindAndValidate(c, &req, "json"); err != nil {
		return
	}

	// 使用通用更新函数
	err := utils.UpdateEntity("task", req.ID, req)
	if err != nil {
		if err.Error() == "entity not found" {
			utils.RespondNotFound(c, "任务不存在")
		} else {
			utils.RespondError(c, "更新任务失败", err)
		}
		return
	}

	utils.RespondSuccess(c, nil, "任务更新成功")
}

// ==================== 删除任务 ====================
// @DeleteMapping("/task/delete")
// @Description: 删除任务
type TaskDeleteRequest struct {
	ID string `json:"id" validate:"required,objectid" description:"任务ID"`
}

func init() {
	router.Api.DELETE("/task/delete", cleanTaskController.DeleteTask)
}

func (tc *CleanTaskController) DeleteTask(c *gin.Context) {
	var req TaskDeleteRequest
	
	if err := utils.BindAndValidate(c, &req, "json"); err != nil {
		return
	}

	err := utils.DeleteEntity("task", req.ID)
	if err != nil {
		if err.Error() == "entity not found" {
			utils.RespondNotFound(c, "任务不存在")
		} else {
			utils.RespondError(c, "删除任务失败", err)
		}
		return
	}

	utils.RespondSuccess(c, nil, "任务删除成功")
}

// ==================== 获取单个任务 ====================
// @GetMapping("/task/get")
// @Description: 根据ID获取任务详情
type TaskGetRequest struct {
	ID string `form:"id" validate:"required,objectid" description:"任务ID"`
}

func init() {
	router.Api.GET("/task/get", cleanTaskController.GetTask)
}

func (tc *CleanTaskController) GetTask(c *gin.Context) {
	var req TaskGetRequest
	
	if err := utils.BindAndValidate(c, &req, "query"); err != nil {
		return
	}

	result, err := utils.GetEntityByID[model.Task]("task", req.ID)
	if err != nil {
		utils.RespondNotFound(c, "任务不存在")
		return
	}

	utils.RespondSuccess(c, result, "获取任务成功")
}

// ==================== 任务特有的业务方法 ====================

// GetTaskStats 获取任务统计信息 - 这是任务控制器特有的业务逻辑
func init() {
	router.Api.GET("/task/stats", cleanTaskController.GetTaskStats)
}

func (tc *CleanTaskController) GetTaskStats(c *gin.Context) {
	// 这里可以实现任务特有的统计逻辑
	// 例如：任务总数、今日创建任务数、任务状态分布等
	
	// 获取所有任务进行统计
	allTasks, err := utils.GetEntityByID[[]model.Task]("task", "")
	if err != nil {
		// 如果获取失败，返回基础统计
		stats := map[string]interface{}{
			"total_tasks": 0,
			"message":     "无法获取详细统计",
		}
		utils.RespondSuccess(c, stats, "获取任务统计成功")
		return
	}

	// 计算统计信息（这里是任务控制器特有的业务逻辑）
	stats := map[string]interface{}{
		"total_tasks": len(*allTasks),
		// 可以添加更多任务相关的统计
		// "completed_tasks": countCompletedTasks(allTasks),
		// "pending_tasks": countPendingTasks(allTasks),
		// "today_created": countTodayCreated(allTasks),
	}

	utils.RespondSuccess(c, stats, "获取任务统计成功")
}

// BatchDeleteTasks 批量删除任务 - 任务特有的业务逻辑
type BatchDeleteRequest struct {
	IDs []string `json:"ids" validate:"required,min=1" description:"任务ID列表"`
}

func init() {
	router.Api.POST("/task/batch-delete", cleanTaskController.BatchDeleteTasks)
}

func (tc *CleanTaskController) BatchDeleteTasks(c *gin.Context) {
	var req BatchDeleteRequest
	
	if err := utils.BindAndValidate(c, &req, "json"); err != nil {
		return
	}

	// 批量删除逻辑（任务特有的业务逻辑）
	var failedIDs []string
	var successCount int

	for _, id := range req.IDs {
		err := utils.DeleteEntity("task", id)
		if err != nil {
			failedIDs = append(failedIDs, id)
		} else {
			successCount++
		}
	}

	result := map[string]interface{}{
		"success_count": successCount,
		"failed_count":  len(failedIDs),
		"failed_ids":    failedIDs,
	}

	if len(failedIDs) == 0 {
		utils.RespondSuccess(c, result, "批量删除成功")
	} else {
		utils.RespondSuccess(c, result, "批量删除部分成功")
	}
}
