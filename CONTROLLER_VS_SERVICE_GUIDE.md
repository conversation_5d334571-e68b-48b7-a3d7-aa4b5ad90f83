# 🎯 Controller 层 vs Service 层 代码分层指南

## 📋 **核心原则**

### **🎮 Controller 层职责**
- ✅ **HTTP 请求处理**：路径参数、查询参数、请求体
- ✅ **参数绑定和基础验证**：使用 `utils.BindAndValidate`
- ✅ **调用 Service 层**：不包含业务逻辑
- ✅ **HTTP 响应处理**：状态码、响应格式
- ✅ **错误处理和响应**：将业务错误转换为HTTP响应

### **🏢 Service 层职责**
- ✅ **业务逻辑**：验证、计算、业务规则
- ✅ **数据处理**：数据转换、组装、清理
- ✅ **业务验证**：邮箱格式、密码强度、业务规则检查
- ✅ **调用 Repository 层**：数据访问
- ✅ **响应构建**：构建业务响应对象

---

## 🔍 **具体对比示例**

### **1. 创建用户功能**

#### **❌ 错误方式：Controller 包含业务逻辑**
```go
// ❌ Controller 层不应该包含这些逻辑
func (uc *UserController) CreateUser(c *gin.Context) {
    var req UserCreateRequest
    utils.BindAndValidate(c, &req, "json")

    // ❌ 业务验证应该在 Service 层
    if !isValidEmail(req.Email) {
        resp := response.Fail("邮箱格式不正确")
        c.JSON(resp.GetHTTPStatus(), resp)
        return
    }

    // ❌ 密码加密应该在 Service 层
    hashedPassword, _ := hashPassword(req.Password)

    // ❌ 数据库操作应该在 Repository 层
    user := &model.User{
        Username: req.Username,
        Email:    req.Email,
        Password: hashedPassword,
    }
    
    // ❌ 直接调用数据库
    result, err := utils.CreateEntity("user", user)
    
    resp := response.SuccessWithMessage(result, "用户创建成功")
    c.JSON(resp.GetHTTPStatus(), resp)
}
```

#### **✅ 正确方式：分层清晰**

**Controller 层：只处理 HTTP**
```go
// ✅ Controller 层：只处理 HTTP 相关
func (uc *UserController) CreateUser(c *gin.Context) {
    var req service.CreateUserRequest
    
    // ✅ HTTP 参数绑定
    if err := utils.BindAndValidate(c, &req, "json"); err != nil {
        return
    }

    // ✅ 调用业务逻辑层
    user, err := uc.userService.CreateUser(context.Background(), &req)
    if err != nil {
        // ✅ HTTP 错误响应
        resp := response.Fail(err.Error())
        c.JSON(resp.GetHTTPStatus(), resp)
        return
    }

    // ✅ HTTP 成功响应
    resp := response.SuccessWithMessage(user, "用户创建成功")
    c.JSON(resp.GetHTTPStatus(), resp)
}
```

**Service 层：处理业务逻辑**
```go
// ✅ Service 层：处理业务逻辑
func (us *UserService) CreateUser(ctx context.Context, req *CreateUserRequest) (*model.User, error) {
    // ✅ 业务验证：邮箱格式
    if !us.isValidEmail(req.Email) {
        return nil, fmt.Errorf("邮箱格式不正确")
    }

    // ✅ 业务验证：密码强度
    if !us.isValidPassword(req.Password) {
        return nil, fmt.Errorf("密码必须至少8位，包含字母和数字")
    }

    // ✅ 业务规则：检查邮箱是否已存在
    exists, err := us.isEmailExists(ctx, req.Email)
    if err != nil {
        return nil, fmt.Errorf("检查邮箱失败: %v", err)
    }
    if exists {
        return nil, fmt.Errorf("邮箱已存在")
    }

    // ✅ 业务逻辑：密码加密
    hashedPassword, err := us.hashPassword(req.Password)
    if err != nil {
        return nil, fmt.Errorf("密码加密失败: %v", err)
    }

    // ✅ 业务逻辑：构建用户实体
    user := &model.User{
        Username:  req.Username,
        Email:     req.Email,
        Password:  hashedPassword,
        Status:    "active",
        CreatedAt: time.Now(),
        UpdatedAt: time.Now(),
    }

    // ✅ 调用数据层
    createdUser, err := us.repository.Create(ctx, user)
    if err != nil {
        return nil, fmt.Errorf("创建用户失败: %v", err)
    }

    // ✅ 业务逻辑：清除敏感信息
    us.clearSensitiveInfo(createdUser)

    return createdUser, nil
}
```

---

### **2. 修改密码功能**

#### **❌ 错误方式：Controller 包含业务逻辑**
```go
// ❌ Controller 层包含太多业务逻辑
func (uc *UserController) ChangePassword(c *gin.Context) {
    var req ChangePasswordRequest
    utils.BindAndValidate(c, &req, "json")

    // ❌ 数据库查询应该在 Repository 层
    user, err := utils.GetEntityByID[model.User]("user", req.UserID)
    
    // ❌ 密码验证应该在 Service 层
    if !uc.verifyPassword(req.OldPassword, user.Password) {
        resp := response.Fail("旧密码错误")
        c.JSON(resp.GetHTTPStatus(), resp)
        return
    }

    // ❌ 密码加密应该在 Service 层
    hashedNewPassword, _ := uc.hashPassword(req.NewPassword)

    // ❌ 数据库更新应该在 Repository 层
    updateData := map[string]interface{}{
        "password": hashedNewPassword,
    }
    utils.UpdateEntity("user", req.UserID, updateData)
    
    resp := response.SuccessWithMessage(nil, "密码修改成功")
    c.JSON(resp.GetHTTPStatus(), resp)
}
```

#### **✅ 正确方式：分层清晰**

**Controller 层：只处理 HTTP**
```go
// ✅ Controller 层：只处理 HTTP
func (uc *UserController) ChangePassword(c *gin.Context) {
    userID := c.Param("id")
    
    var req service.ChangePasswordRequest
    if err := utils.BindAndValidate(c, &req, "json"); err != nil {
        return
    }

    // ✅ 调用业务逻辑层
    err := uc.userService.ChangePassword(context.Background(), userID, &req)
    if err != nil {
        resp := response.Fail(err.Error())
        c.JSON(resp.GetHTTPStatus(), resp)
        return
    }

    resp := response.SuccessWithMessage(nil, "密码修改成功")
    c.JSON(resp.GetHTTPStatus(), resp)
}
```

**Service 层：处理业务逻辑**
```go
// ✅ Service 层：处理业务逻辑
func (us *UserService) ChangePassword(ctx context.Context, userID string, req *ChangePasswordRequest) error {
    // ✅ 业务验证：获取用户
    user, err := us.repository.FindOne(ctx, bson.M{"_id": userID})
    if err != nil {
        return fmt.Errorf("用户不存在: %v", err)
    }

    // ✅ 业务验证：验证旧密码
    if !us.checkPassword(req.OldPassword, user.Password) {
        return fmt.Errorf("旧密码不正确")
    }

    // ✅ 业务验证：新密码强度检查
    if !us.isValidPassword(req.NewPassword) {
        return fmt.Errorf("新密码必须至少8位，包含字母和数字")
    }

    // ✅ 业务逻辑：加密新密码
    hashedPassword, err := us.hashPassword(req.NewPassword)
    if err != nil {
        return fmt.Errorf("密码加密失败: %v", err)
    }

    // ✅ 调用数据层更新密码
    _, err = us.repository.UpdateByFilter(ctx, bson.M{"_id": userID}, bson.M{
        "password":   hashedPassword,
        "updated_at": time.Now(),
    })
    if err != nil {
        return fmt.Errorf("更新密码失败: %v", err)
    }

    return nil
}
```

---

## 📝 **判断代码应该放在哪一层的方法**

### **🤔 问自己这些问题：**

1. **这段代码是否与 HTTP 请求/响应直接相关？**
   - ✅ 是 → Controller 层
   - ❌ 否 → Service 层

2. **这段代码是否包含业务规则或验证？**
   - ✅ 是 → Service 层
   - ❌ 否 → 可能是 Controller 层

3. **这段代码是否需要访问数据库？**
   - ✅ 是 → Repository 层（通过 Service 层调用）
   - ❌ 否 → Controller 或 Service 层

4. **这段代码是否可以独立测试（不依赖 HTTP）？**
   - ✅ 是 → Service 层
   - ❌ 否 → Controller 层

### **🎯 具体示例：**

| 代码内容 | 应该放在 | 原因 |
|---------|---------|------|
| `c.Param("id")` | Controller | HTTP 路径参数获取 |
| `utils.BindAndValidate(c, &req, "json")` | Controller | HTTP 参数绑定 |
| `isValidEmail(email)` | Service | 业务验证逻辑 |
| `bcrypt.GenerateFromPassword()` | Service | 业务逻辑（密码加密） |
| `repository.FindOne()` | Service调用Repository | 数据访问 |
| `response.SuccessWithMessage()` | Controller | HTTP 响应构建 |
| `c.JSON(status, data)` | Controller | HTTP 响应发送 |
| `time.Now()` | Service | 业务逻辑（时间戳） |
| `fmt.Errorf("业务错误")` | Service | 业务错误处理 |

---

## 🎉 **总结**

### **Controller 层 = HTTP 处理器**
- 只关心 HTTP 请求和响应
- 不包含任何业务逻辑
- 薄薄的一层，主要是调用 Service

### **Service 层 = 业务逻辑处理器**
- 包含所有业务规则和验证
- 处理数据转换和组装
- 调用 Repository 进行数据访问
- 可以独立测试，不依赖 HTTP

这样分层的好处：
1. **职责清晰** - 每层只关注自己的职责
2. **易于测试** - Service 层可以独立测试
3. **易于维护** - 业务逻辑集中在 Service 层
4. **易于复用** - Service 层可以被多个 Controller 使用
