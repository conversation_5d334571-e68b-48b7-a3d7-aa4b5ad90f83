# MongoDB迁移指南

## 为什么使用内存数据库

在项目清理过程中，遇到了严重的MongoDB驱动版本冲突：

### 问题分析
1. **版本冲突**：项目同时依赖 `go.mongodb.org/mongo-driver v1.17.4` 和 `go.mongodb.org/mongo-driver/v2 v2.2.2`
2. **go-mongox兼容性**：`github.com/chenmingyong0423/go-mongox/v2` 与某个版本不兼容
3. **编译错误**：方法不存在、类型不匹配等错误

### 解决方案
选择内存数据库的原因：
- ✅ 快速解决版本冲突问题
- ✅ 保证项目可以正常运行
- ✅ 不需要外部MongoDB服务
- ✅ 便于演示和测试
- ✅ 架构层保持一致，易于后续切换

## 如何切换回MongoDB

### 步骤1：清理依赖冲突

```bash
# 1. 删除冲突的依赖
go mod edit -droprequire go.mongodb.org/mongo-driver/v2
go mod edit -droprequire github.com/chenmingyong0423/go-mongox/v2

# 2. 只保留一个版本的MongoDB驱动
go get go.mongodb.org/mongo-driver@latest

# 3. 清理模块缓存
go mod tidy
```

### 步骤2：实现MongoDB数据库层

创建 `database/mongodb.go`：

```go
package database

import (
    "context"
    "demo/config"
    "fmt"
    "log"
    "time"

    "go.mongodb.org/mongo-driver/mongo"
    "go.mongodb.org/mongo-driver/mongo/options"
)

var mongoClient *mongo.Client
var mongoDB *mongo.Database

func InitDB() error {
    cfg := config.AppConfig.MongoDB
    
    // 构建连接字符串
    uri := fmt.Sprintf("mongodb://%s:%d", cfg.Host, cfg.Port)
    if cfg.Username != "" && cfg.Password != "" {
        uri = fmt.Sprintf("mongodb://%s:%s@%s:%d", 
            cfg.Username, cfg.Password, cfg.Host, cfg.Port)
    }
    
    // 连接选项
    clientOptions := options.Client().ApplyURI(uri)
    clientOptions.SetMaxPoolSize(100)
    clientOptions.SetMinPoolSize(10)
    clientOptions.SetMaxConnIdleTime(30 * time.Second)
    
    // 连接MongoDB
    ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
    defer cancel()
    
    client, err := mongo.Connect(ctx, clientOptions)
    if err != nil {
        return fmt.Errorf("failed to connect to MongoDB: %v", err)
    }
    
    // 测试连接
    if err := client.Ping(ctx, nil); err != nil {
        return fmt.Errorf("failed to ping MongoDB: %v", err)
    }
    
    mongoClient = client
    mongoDB = client.Database(cfg.DBName)
    
    log.Println("MongoDB connected successfully")
    return nil
}

func CloseDB() error {
    if mongoClient != nil {
        ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
        defer cancel()
        return mongoClient.Disconnect(ctx)
    }
    return nil
}

func GetCollection(name string) *mongo.Collection {
    return mongoDB.Collection(name)
}
```

### 步骤3：实现MongoDB CRUD操作

```go
// database/mongodb_operations.go
package database

import (
    "context"
    "demo/response"
    "fmt"
    "time"

    "go.mongodb.org/mongo-driver/bson"
    "go.mongodb.org/mongo-driver/bson/primitive"
    "go.mongodb.org/mongo-driver/mongo/options"
)

func Create(collectionName string, entity interface{}) (interface{}, error) {
    collection := GetCollection(collectionName)
    ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
    defer cancel()
    
    result, err := collection.InsertOne(ctx, entity)
    if err != nil {
        return nil, err
    }
    
    return result.InsertedID, nil
}

func FindByID(collectionName string, id string, result interface{}) error {
    collection := GetCollection(collectionName)
    ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
    defer cancel()
    
    objectID, err := primitive.ObjectIDFromHex(id)
    if err != nil {
        return fmt.Errorf("invalid ID format")
    }
    
    return collection.FindOne(ctx, bson.M{"_id": objectID}).Decode(result)
}

func Update(collectionName string, id string, update interface{}) error {
    collection := GetCollection(collectionName)
    ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
    defer cancel()
    
    objectID, err := primitive.ObjectIDFromHex(id)
    if err != nil {
        return fmt.Errorf("invalid ID format")
    }
    
    _, err = collection.UpdateOne(
        ctx,
        bson.M{"_id": objectID},
        bson.M{"$set": update},
    )
    
    return err
}

func Delete(collectionName string, id string) error {
    collection := GetCollection(collectionName)
    ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
    defer cancel()
    
    objectID, err := primitive.ObjectIDFromHex(id)
    if err != nil {
        return fmt.Errorf("invalid ID format")
    }
    
    _, err = collection.DeleteOne(ctx, bson.M{"_id": objectID})
    return err
}

func QueryWithPagination(collectionName string, params map[string]interface{}) (*response.PagedResponse, error) {
    collection := GetCollection(collectionName)
    ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
    defer cancel()
    
    // 解析分页参数
    page := int64(1)
    size := int64(10)
    
    if p, ok := params["page"].(int64); ok && p > 0 {
        page = p
    }
    if s, ok := params["size"].(int64); ok && s > 0 && s <= 1000 {
        size = s
    }
    
    // 构建查询条件
    filter := bson.M{}
    // 这里可以添加更多查询条件解析逻辑
    
    // 计算总数
    total, err := collection.CountDocuments(ctx, filter)
    if err != nil {
        return nil, err
    }
    
    // 查询数据
    skip := (page - 1) * size
    findOptions := options.Find().SetSkip(skip).SetLimit(size)
    
    cursor, err := collection.Find(ctx, filter, findOptions)
    if err != nil {
        return nil, err
    }
    defer cursor.Close(ctx)
    
    var results []interface{}
    if err := cursor.All(ctx, &results); err != nil {
        return nil, err
    }
    
    // 构建分页响应
    totalPages := (total + size - 1) / size
    
    return &response.PagedResponse{
        Data: results,
        Pagination: response.PaginationInfo{
            Page:       int(page),
            Size:       int(size),
            Total:      int(total),
            TotalPages: int(totalPages),
            HasNext:    page < totalPages,
            HasPrev:    page > 1,
        },
    }, nil
}
```

### 步骤4：更新配置

确保 `.env` 文件包含正确的MongoDB配置：

```env
# MongoDB Configuration
MONGODB_HOST=localhost
MONGODB_PORT=27017
MONGODB_DB_NAME=demoDb
MONGODB_USERNAME=
MONGODB_PASSWORD=

# 或者使用您的MongoDB服务器配置
# MONGODB_HOST=***************
# MONGODB_PORT=37017
# MONGODB_USERNAME=xroot
# MONGODB_PASSWORD=xrootpwd!
```

### 步骤5：测试切换

1. 确保MongoDB服务正在运行
2. 重新编译项目：`go build`
3. 运行项目：`./demo.exe`
4. 测试API端点确保功能正常

## 总结

内存数据库是一个临时解决方案，用于：
- 快速解决版本冲突问题
- 确保项目演示功能正常
- 提供完整的API框架参考

当您准备好使用真实的MongoDB时，可以按照上述步骤进行切换。架构设计使得这种切换相对简单，只需要替换数据库层的实现即可。
